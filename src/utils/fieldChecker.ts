import type { PreparedDynamicReplacementContract } from "@/app/components/create-job-form/types";

export const checkFields = (
  orgName: string,
  repoName: string,
  ref: string,
  preparedContracts?: PreparedDynamicReplacementContract[]
): boolean => {
  if (!orgName && !repoName && !ref) {
    alert("Please fill out the organization, repository, and branch fields");
    return false;
  }
  if (!orgName) {
    alert("Please fill out the organization field");
    return false;
  }
  if (!repoName) {
    alert("Please fill out the repository field");
    return false;
  }
  if (!ref) {
    alert("Please fill out the branch field");
    return false;
  }

  if (preparedContracts && preparedContracts.length > 0) {
    preparedContracts.forEach((contract, index) => {
      if (contract.target === "") {
        alert("Please fill out the target name field");
        return false;
      }

      if (contract.replacement === "") {
        alert("Please fill out the replacement name field");
        return false;
      }

      if (contract.endOfTargetMarker === "") {
        alert("Please fill out the endOfTargetMarker name field");
        return false;
      }

      if (contract.targetContract === "") {
        alert("Please fill out the targetContract name field");
        return false;
      }
    });
  }

  if (preparedContracts && preparedContracts.length === 0) {
    alert("Please fill out the Dynamic replacement fields");
    return false;
  }

  return true;
};
