"use client";

import { ABIActionsRow } from "@/app/(app)/dashboard/handlers/[identifier]/abi-actions-row";
import { ABIProvider } from "@/app/(app)/dashboard/handlers/[identifier]/abi-context";
import { BeforeAfterContracts } from "@/app/(app)/dashboard/handlers/[identifier]/before-after-contracts";
import { ResultsPage } from "@/app/(app)/dashboard/handlers/[identifier]/results-page";
import { SelectContracts } from "@/app/(app)/dashboard/handlers/[identifier]/select-contracts";
import React, { useState } from "react";
import { AppButton } from "../../components/app-button";
import { AppTabs, AppTabPane } from "../../components/app-tabs";
import { AppInput } from "../../components/app-input";
import { AppTextarea } from "../../components/app-textarea";
import { Title2Strong, Body1 } from "../../components/app-typography";

// Constants for sandbox builder
const SANDBOX_CONFIG = {
  DEFAULT_CONTRACT_NAME: "Your_contract",
  DEFAULT_ABI_PLACEHOLDER: (index: number) => `Paste ABI ${index + 1} here`,
  CONTRACT_NAME_PLACEHOLDER: "Contract name",
} as const;

// Types
interface AbiInput {
  content: string;
  name: string;
  id: string;
}

interface ParsedAbi {
  name: string;
  abi: any;
  abiPath: string;
}

// Utility functions
const capitalizeFirstLetter = (str: string): string => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

const parseAbiInput = (input: AbiInput, index: number): ParsedAbi => {
  const obj = JSON.parse(input.content);
  return {
    name: input.name,
    abi: obj?.abi ? obj.abi : obj,
    abiPath: `src${index}`,
  };
};

// Reusable components
interface AbiInputFieldProps {
  input: AbiInput;
  index: number;
  onContentChange: (index: number, value: string) => void;
  onNameChange: (index: number, value: string) => void;
}

function AbiInputField({
  input,
  index,
  onContentChange,
  onNameChange,
}: AbiInputFieldProps) {
  return (
    <div className="mb-6 w-full">
      <AppTextarea
        label={`ABI ${index + 1}`}
        value={input.content}
        onChange={(e) => onContentChange(index, e.target.value)}
        placeholder={SANDBOX_CONFIG.DEFAULT_ABI_PLACEHOLDER(index)}
        rows={6}
        className="mb-4"
      />
      <AppInput
        label="Contract Name"
        value={input.name}
        onChange={(e) => onNameChange(index, e.target.value)}
        placeholder={SANDBOX_CONFIG.CONTRACT_NAME_PLACEHOLDER}
        type="text"
      />
    </div>
  );
}

interface ErrorDisplayProps {
  errors: string[];
}

function ErrorDisplay({ errors }: ErrorDisplayProps) {
  if (errors.length === 0) return null;

  return (
    <div className="mb-4 rounded-lg border border-red-200 bg-red-50 p-4 text-center">
      <Body1 color="primary" className="text-red-600">
        {errors.join(", ")}
      </Body1>
    </div>
  );
}

interface HandlersBuilderProps {
  preparedAbis: ParsedAbi[];
}

function HandlersBuilder({ preparedAbis }: HandlersBuilderProps) {
  return (
    <ABIProvider identifier={preparedAbis} forced={true}>
      <Title2Strong color="primary" className="mb-5">
        Build your Handlers
      </Title2Strong>
      <div className="flex gap-10">
        <AppTabs defaultActiveKey="select-contacts" className="min-w-[400px]">
          <AppTabPane tab="select-contacts" label="Contracts">
            <SelectContracts />
          </AppTabPane>
          <AppTabPane
            tab="before-after-contacts"
            label="Before and after trackers"
          >
            <BeforeAfterContracts />
          </AppTabPane>
        </AppTabs>
        <div className="min-w-[500px] grow">
          <ABIActionsRow />
          <ResultsPage />
        </div>
      </div>
    </ABIProvider>
  );
}

export default function SandboxBuilder() {
  const [abiInputs, setAbiInputs] = useState<AbiInput[]>([
    {
      content: "",
      name: SANDBOX_CONFIG.DEFAULT_CONTRACT_NAME,
      id: crypto.randomUUID(),
    },
  ]);
  const [preparedAbis, setPreparedAbis] = useState<ParsedAbi[] | null>(null);
  const [errors, setErrors] = useState<string[]>([]);

  const addNewAbi = () => {
    setAbiInputs([
      ...abiInputs,
      {
        content: "",
        name: SANDBOX_CONFIG.DEFAULT_CONTRACT_NAME,
        id: crypto.randomUUID(),
      },
    ]);
  };

  const handleAbiChange = (index: number, value: string) => {
    const newInputs = [...abiInputs];
    newInputs[index].content = value;
    setAbiInputs(newInputs);
  };

  const handleNameChange = (index: number, value: string) => {
    const newInputs = [...abiInputs];
    const capitalizedValue = capitalizeFirstLetter(value);
    newInputs[index].name = capitalizedValue;
    setAbiInputs(newInputs);

    try {
      const parsed = newInputs.map(parseAbiInput);
      setPreparedAbis(parsed);
      setErrors([]);
    } catch (e) {
      setErrors(["Invalid JSON in one or more ABIs"]);
    }
  };

  const parseAbis = () => {
    try {
      const parsed = abiInputs.map(parseAbiInput);
      console.log(parsed);
      setPreparedAbis(parsed);
      setErrors([]);
    } catch (e) {
      setErrors(["Invalid JSON in one or more ABIs"]);
    }
  };

  return (
    <div className="w-full">
      <div className="mb-6">
        {abiInputs.map((input, index) => (
          <AbiInputField
            key={input.id}
            input={input}
            index={index}
            onContentChange={handleAbiChange}
            onNameChange={handleNameChange}
          />
        ))}
      </div>

      <div className="mb-6 flex items-center justify-center gap-4">
        <AppButton onClick={addNewAbi} variant="secondary">
          Add Another ABI
        </AppButton>
        <AppButton onClick={parseAbis} variant="primary">
          Parse ABIs
        </AppButton>
      </div>

      <ErrorDisplay errors={errors} />

      {preparedAbis && <HandlersBuilder preparedAbis={preparedAbis} />}
    </div>
  );
}
