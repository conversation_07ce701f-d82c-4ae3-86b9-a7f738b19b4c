"use client";
import Link from "next/link";
import { useState } from "react";
import { Fi<PERSON>rrowLeft, FiPlay, FiX } from "react-icons/fi";

import type { ENV_TYPE } from "@/app/app.constants";
import { AppButton } from "@/app/components/app-button";
import { AppInput } from "@/app/components/app-input";
import { Body4, H1 } from "@/app/components/app-typography";
import { GradientWrapper } from "@/app/components/gradient-wrapper";

import { AppHeader } from "@/app/(app)/components/app-header";
import LogComponent from "@/app/(app)/tools/logs-parser/logs-parser";
import { AppTextarea } from "@/app/components/app-textarea";
import VideoPlayer from "@/app/components/VideoPlayer/VideoPlayer";

interface ToolPageLayoutProps {
  toolType: ENV_TYPE;
  toolName: string;
  toolDescription: string[];
  youtubeUrl?: string;
  youtubeOverlayText?: string;
}

export default function ToolPageLayout({
  toolType,
  toolName,
  toolDescription,
  youtubeUrl,
  youtubeOverlayText,
}: ToolPageLayoutProps) {
  const [logs, setLogs] = useState("");
  const [prefix, setPrefix] = useState("");
  const [isVideoVisible, setIsVideoVisible] = useState(false);

  return (
    <div className="main-container w-full overflow-x-hidden">
      <div className="relative z-10 min-h-screen">
        <AppHeader skipUser />

        <GradientWrapper className="flex min-h-[calc(100vh-80px)] items-center justify-center py-8">
          <div className="w-full max-w-4xl rounded-[20px] bg-back-neutral-tertiary px-[48px] py-[40px]">
            <div className="mb-8 text-left">
              <Link
                href="/tools"
                className="mb-6 inline-flex items-center text-fore-neutral-primary transition-colors duration-200 hover:text-fore-neutral-secondary"
              >
                <FiArrowLeft className="size-10" />
              </Link>

              <H1 className="mb-6 text-accent-primary">{toolName}</H1>

              {toolDescription.map((desc, index) => (
                <Body4
                  key={`desc-${desc.slice(0, 20)}-${index}`}
                  className="mb-2"
                >
                  {desc}
                </Body4>
              ))}
            </div>

            {youtubeUrl && (
              <div className="mb-8">
                <AppButton
                  onClick={() => setIsVideoVisible(!isVideoVisible)}
                  variant="primary"
                  className="mb-4 transition-transform duration-200 hover:scale-105"
                  leftIcon={isVideoVisible ? <FiX /> : <FiPlay />}
                >
                  {isVideoVisible
                    ? "Hide Tutorial Video"
                    : "Show Tutorial Video"}
                </AppButton>

                <div
                  className={`overflow-hidden transition-all duration-700 ease-in-out ${
                    isVideoVisible
                      ? "max-h-[600px] translate-y-0 scale-100 opacity-100"
                      : "max-h-0 -translate-y-8 scale-95 opacity-0"
                  }`}
                >
                  <div
                    className={`transition-all duration-500 ${
                      isVideoVisible ? "transform-none" : "scale-95"
                    }`}
                  >
                    <VideoPlayer
                      link={youtubeUrl}
                      overlayText={youtubeOverlayText}
                    />
                  </div>
                </div>
              </div>
            )}

            <AppInput
              className="mb-6"
              label="Add a Prefix for your convenience"
              value={prefix}
              onChange={(e) => setPrefix(e.target.value)}
              type="text"
            />

            <AppTextarea
              className="mb-6"
              label={`Paste ${toolName.split(" ")[0]} Logs Here`}
              value={logs}
              onChange={(e) => setLogs(e.target.value)}
            />

            <LogComponent fuzzer={toolType} logs={logs} prefix={prefix} />
          </div>
        </GradientWrapper>
      </div>
    </div>
  );
}
