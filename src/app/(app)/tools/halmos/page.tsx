import type { Metadata } from "next";
import { ENV_TYPE } from "@/app/app.constants";
import ToolPageLayout from "../../components/tool-layout";

export const metadata: Metadata = {
  title: "Halmos Logs Parser",
  description:
    "Paste Halmos Logs and automatically convert broken properties into Foundry Repros",
};

export default function HalmosParserPage() {
  return (
    <ToolPageLayout
      toolType={ENV_TYPE.HALMOS}
      toolName="Halmos Logs Scraper"
      toolDescription={[
        "This tool allows to scrape halmos logs for broken properties repros",
        "Paste your raw halmos logs, and the tool will generate foundry repros for you",
      ]}
      youtubeUrl="https://www.youtube.com/embed/WDdFVZpTAZo"
      youtubeOverlayText="Learn how to use Halmos"
    />
  );
}
