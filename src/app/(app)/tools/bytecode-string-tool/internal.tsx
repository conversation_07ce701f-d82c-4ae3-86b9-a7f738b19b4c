"use client";
import { useMemo, useState } from "react";
import Link from "next/link";
import { FiArrowLeft } from "react-icons/fi";

import { AppTextarea } from "@/app/components/app-textarea";
import { AppCode } from "@/app/components/app-code";
import { Body2, H1, H3 } from "@/app/components/app-typography";
import { GradientWrapper } from "@/app/components/gradient-wrapper";
import { AppHeader } from "@/app/(app)/components/app-header";

function stringToHex(input: string, padToBytes: number = 0): string {
  // Convert string to UTF-8 bytes
  const encoder = new TextEncoder();
  const bytes = encoder.encode(input);

  // Convert bytes to hex
  const hex = Array.from(bytes)
    .map((byte) => byte.toString(16).padStart(2, "0"))
    .join("");

  // Add length prefix (number of bytes)
  const lengthHex = bytes.length.toString(16).padStart(2, "0");
  const result = `0x${lengthHex}${hex}`;

  // Pad if requested
  if (padToBytes > 0) {
    const currentLength = result.length - 2; // Subtract '0x'
    const paddingNeeded = padToBytes * 2 - currentLength;
    if (paddingNeeded > 0) {
      return result + "0".repeat(paddingNeeded);
    }
  }

  return result;
}

export default function BytecodeStringToolInternal() {
  const [string, setString] = useState("");

  const encoded = useMemo(() => {
    return stringToHex(string);
  }, [string]);

  const padded = useMemo(() => {
    return stringToHex(string, 32);
  }, [string]);

  return (
    <div className="main-container w-full overflow-x-hidden">
      <div className="relative z-10 min-h-screen">
        <AppHeader skipUser />

        <GradientWrapper className="flex min-h-[calc(100vh-80px)] items-center justify-center py-8">
          <div className="w-full max-w-4xl rounded-[20px] bg-back-neutral-tertiary px-[48px] py-[40px]">
            <div className="mb-8 text-left">
              <Link
                href="/tools"
                className="mb-6 inline-flex items-center text-fore-neutral-primary transition-colors duration-200 hover:text-fore-neutral-secondary"
              >
                <FiArrowLeft className="size-10" />
              </Link>

              <H1 className="mb-6 text-accent-primary">Bytecode String Tool</H1>

              <Body2 className="mb-2">
                This tool encodes strings into UTF-8 Bytes
              </Body2>
              <Body2 className="mb-6">
                It also adds a length value as a prefix and pads the length to
                32 bytes
              </Body2>
            </div>

            <div className="mb-8">
              <AppTextarea
                className="mb-6"
                label="String (Max 32 chars)"
                value={string}
                onChange={(e) => setString(e.target.value)}
                placeholder="Enter your string here..."
              />
            </div>

            <div className="space-y-6">
              <div>
                <H3 className="mb-4 text-fore-neutral-primary">Encoded</H3>
                <AppCode
                  showLineNumbers={false}
                  code={encoded}
                  language="python"
                />
              </div>

              <div>
                <H3 className="mb-4 text-fore-neutral-primary">
                  Encoded and Padded
                </H3>
                <AppCode
                  showLineNumbers={false}
                  code={padded}
                  language="python"
                />
              </div>
            </div>
          </div>
        </GradientWrapper>
      </div>
    </div>
  );
}
