import { ENV_TYPE } from "@/app/app.constants";
import type { Metadata } from "next";
import ToolPageLayout from "../../components/tool-layout";

export const metadata: Metadata = {
  title: "Echidna Logs Parser",
  description:
    "Paste Echidna Logs and automatically convert broken properties into Foundry Repros",
};

export default function EchidnaParserPage() {
  return (
    <ToolPageLayout
      toolType={ENV_TYPE.ECHIDNA}
      toolName="Echidna Logs Scraper"
      toolDescription={[
        "This tool allows to scrape echidna logs for broken properties repros",
        "Paste your raw echidna logs, and the tool will generate foundry repros for you",
      ]}
      youtubeUrl="https://www.youtube.com/embed/DHAvBrsITRU"
      youtubeOverlayText="Learn how to use Echidna"
    />
  );
}
