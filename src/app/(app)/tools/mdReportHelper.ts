export const prepareProperties = (
  propRaw: string
): { property: string; status: string } => {
  // Capture Medusa
  if (propRaw.includes("[FAILED]")) {
    return {
      property: propRaw.split(".")[1],
      status: "❌",
    };
  } else if (propRaw.includes("[PASSED]")) {
    return {
      property: propRaw.split(".")[1],
      status: "✅",
    };
    // Capture Echidna
  } else if (propRaw.includes("passing")) {
    return {
      property: propRaw.split(":")[0],
      status: "✅",
    };
  } else if (propRaw.includes("failed")) {
    return {
      property: propRaw.split(":")[0],
      status: "❌",
    };
  }
};

export const sortProperties = (results: string[]) => {
  return results
    .map((data, index) => ({
      data,
      index,
      ...prepareProperties(data),
    }))
    .sort((a, b) => {
      if (a.status === "❌" && b.status !== "❌") return -1;
      if (a.status !== "❌" && b.status === "❌") return 1;
      return 0;
    });
};

export const downloadFile = (markdown: string, repoName: string) => {
  if (markdown) {
    // Convert Markdown content to a Blob
    const blob = new Blob([markdown], { type: "text/markdown" });
    const url = URL.createObjectURL(blob);

    const a = document.createElement("a");
    a.href = url;
    a.download = `${repoName}_fuzzRun_analysis.md`;
    a.style.display = "none";

    document.body.appendChild(a);
    a.click();

    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
};
