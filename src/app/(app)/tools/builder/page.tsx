import type { <PERSON><PERSON><PERSON> } from "next";
import Link from "next/link";
import { FiArrowLeft } from "react-icons/fi";

import { GradientWrapper } from "@/app/components/gradient-wrapper";
import { H1, Body2 } from "@/app/components/app-typography";
import { AppHeader } from "@/app/(app)/components/app-header";
import SandboxBuilder from "@/app/(app)/components/sandbox-builder";

export const metadata: Metadata = {
  title: "Invariant Testing Builder Tool",
  description:
    "Automatically Scaffold a Chimera Invariant Testing Suite from your Contracts ABI",
};

export default function BuilderPage() {
  return (
    <div className="main-container w-full overflow-x-hidden">
      <div className="relative z-10 min-h-screen">
        <AppHeader skipUser />

        <GradientWrapper className="flex min-h-[calc(100vh-80px)] items-center justify-center py-8">
          <div className="w-full max-w-6xl rounded-[20px] bg-back-neutral-tertiary px-[48px] py-[40px]">
            <div className="mb-8 text-left">
              <Link
                href="/"
                className="mb-6 inline-flex items-center text-fore-neutral-primary transition-colors duration-200 hover:text-fore-neutral-secondary"
              >
                <FiArrowLeft className="size-10" />
              </Link>

              <H1 className="mb-6 text-accent-primary">
                Invariant Testing Builder
              </H1>

              <Body2 className="mb-2">
                The sandbox allows you to Scaffold Invariant Tests from your
                Contract ABIs.
              </Body2>
              <Body2 className="mb-2">
                Log in to Recon to scaffold bigger projects with advanced
                features.
              </Body2>
            </div>

            <SandboxBuilder />
          </div>
        </GradientWrapper>
      </div>
    </div>
  );
}
