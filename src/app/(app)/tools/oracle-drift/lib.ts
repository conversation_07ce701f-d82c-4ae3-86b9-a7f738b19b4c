const MAX_BPS = 10_000;

function applyDrift(
  price: number,
  driftBPS: number,
  isUp: boolean
): number {
  let newPrice = price;
  if (isUp) {
    newPrice = (price * (MAX_BPS + driftBPS - 1 / 1e18)) / MAX_BPS;
  } else {
    newPrice = (price * (MAX_BPS - driftBPS + 1 / 1e18)) / MAX_BPS;
  }

  return newPrice;
}

const getEBTCPrice = (coll: number, debt: number, formulaFn?: (coll: number, debt: number) => number) => {
  if (formulaFn) {
    return formulaFn(coll, debt);
  }
  return (coll / debt);
};

export function doubleDrift(
  ethBtcPrice: number,
  ethBtcDriftBPS: number,
  stEthPrice: number,
  stEthDriftBPS: number,
  formulaFn?: (coll: number, debt: number) => number
): { max: number; min: number; spot: number } {
  const spot = getEBTCPrice(ethBtcPrice, stEthPrice, formulaFn);

  let max = 0;
  let min = 0;

// 4 possible prices
  const lowBtc = applyDrift(ethBtcPrice, ethBtcDriftBPS, false);
  const lowstEth = applyDrift(stEthPrice, stEthDriftBPS, false);
  const highBtc = applyDrift(ethBtcPrice, ethBtcDriftBPS, true);
  const highStEth = applyDrift(stEthPrice, stEthDriftBPS, true);

  const ll = getEBTCPrice(lowBtc, lowstEth, formulaFn);
  const lh = getEBTCPrice(lowBtc, highStEth, formulaFn);
  const hl = getEBTCPrice(highBtc, lowstEth, formulaFn);
  const hh = getEBTCPrice(highBtc, highStEth, formulaFn);

  min = Math.min(ll, lh, hl, hh);
  max = Math.max(ll, lh, hl, hh);

  return {
    spot,
    max,
    min,
  };
}