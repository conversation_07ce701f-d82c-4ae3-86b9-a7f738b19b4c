export interface FormulaContext {
  coll: number;
  debt: number;
}

export function evaluateFormula(
  formula: string,
  context: FormulaContext
): number {
  // eslint-disable-next-line no-useless-escape
  const allowedOperators = /^[\d\s\+\-\*\/\(\)\.colldebt]+$/;

  if (!allowedOperators.test(formula)) {
    throw new Error(
      "Invalid formula. Only numbers, operators (+, -, *, /, parentheses), and variables (coll, debt) are allowed."
    );
  }

  const sanitizedFormula = formula
    .replace(/coll/g, context.coll.toString())
    .replace(/debt/g, context.debt.toString());

  try {
    const result = Function(
      '"use strict"; return (' + sanitizedFormula + ")"
    )();

    if (typeof result !== "number" || !isFinite(result)) {
      throw new Error("Formula must return a finite number");
    }

    return result;
  } catch (error) {
    throw new Error(`Error evaluating formula: ${error.message}`);
  }
}

export const defaultFormula = "coll / debt";

export const formulaExamples = [
  { name: "Division (Default)", formula: "coll / debt" },
  { name: "Multiplication", formula: "coll * debt" },
  { name: "Addition", formula: "coll + debt" },
  { name: "Subtraction", formula: "coll - debt" },
  { name: "Complex", formula: "(coll / (10 ** 8)) / (debt * (10 ** 8))" },
];
