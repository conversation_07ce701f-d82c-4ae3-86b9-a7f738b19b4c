"use client";
import { AppTextarea } from "@/app/components/app-textarea";
import type { BytecodeComparisonResult } from "@/lib/utils";
import { compareBytecode } from "@/lib/utils";
import { useState } from "react";
import Link from "next/link";
import { FiArrowLeft } from "react-icons/fi";
import { AppButton } from "../../../components/app-button";
import { Body2, H1, H2, H3, H4, H5 } from "@/app/components/app-typography";
import { AppHeader } from "@/app/(app)/components/app-header";
import { GradientWrapper } from "@/app/components/gradient-wrapper";

// Helper function to render bytecode length display
const BytecodeLengthDisplay = ({
  bytecode,
  label,
}: {
  bytecode: string;
  label: string;
}) => {
  if (bytecode.length === 0) return null;

  return (
    <Body2 className="mb-2">
      {label} length: {bytecode.length}
    </Body2>
  );
};

// Helper function to render comparison result status
const ComparisonStatus = ({ isIdentical }: { isIdentical: boolean }) => {
  if (isIdentical) {
    return (
      <div className="font-bold text-status-success">
        ✅ The bytecodes are functionally identical (ignoring specified metadata
        sections).
      </div>
    );
  }

  return (
    <div className="font-bold text-status-error">
      ❌ The bytecodes are different, even ignoring specified metadata sections.
    </div>
  );
};

// Helper function to render replaced sections
const ReplacedSections = ({
  sections,
  fileNumber,
}: {
  sections: any[];
  fileNumber: number;
}) => {
  if (sections.length === 0) return null;

  return (
    <div className={fileNumber === 2 ? "" : "mb-4"}>
      <H4>
        File {fileNumber} ({sections.length} sections):
      </H4>
      <ul className="list-inside list-disc pl-4">
        {sections.map((section, i) => (
          <li key={i} className="mb-1">
            {section.type} at position {section.position}-
            {section.position + section.length - 1}
            <div className="text-xs text-fore-neutral-secondary">
              Content: {section.content.substring(0, 30)}
              {section.content.length > 30 ? "..." : ""}
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default function BytecodeCompareInternal() {
  const [bytecodeOne, setBytecodeOne] = useState("");
  const [bytecodeTwo, setBytecodeTwo] = useState("");
  const [ignoreHash, setIgnoreHash] = useState(true);
  const [ignoreCbor, setIgnoreCbor] = useState(true);
  const [comparisonResult, setComparisonResult] =
    useState<BytecodeComparisonResult | null>(null);
  const [isComparing, setIsComparing] = useState(false);

  const handleCompare = () => {
    if (!bytecodeOne || !bytecodeTwo) {
      alert("Please provide both bytecode inputs");
      return;
    }

    setIsComparing(true);
    try {
      const result = compareBytecode(
        bytecodeOne,
        bytecodeTwo,
        ignoreHash,
        ignoreCbor
      );
      setComparisonResult(result);
    } catch (error) {
      console.error("Error during bytecode comparison:", error);
      alert("An error occurred during comparison");
    } finally {
      setIsComparing(false);
    }
  };

  return (
    <div className="main-container w-full overflow-x-hidden">
      <div className="relative z-10 min-h-screen">
        <AppHeader skipUser />

        <GradientWrapper className="flex min-h-[calc(100vh-80px)] items-center justify-center py-8">
          <div className="w-full max-w-6xl rounded-[20px] bg-back-neutral-tertiary px-[48px] py-[40px]">
            <div className="mb-8 text-left">
              <Link
                href="/tools"
                className="mb-6 inline-flex items-center text-fore-neutral-primary transition-colors duration-200 hover:text-fore-neutral-secondary"
              >
                <FiArrowLeft className="size-10" />
              </Link>

              <H1 className="mb-6 text-accent-primary">Bytecode Compare</H1>

              <Body2 className="mb-2">
                This tool compares two bytecode strings to identify differences
              </Body2>
              <Body2 className="mb-2">
                Options for ignoring bytecode hash and CBOR metadata help
                identify functional differences
              </Body2>
            </div>
            <div className="grid grid-cols-2 gap-10">
              <div className="flex flex-col">
                <AppTextarea
                  className="mb-[8px]"
                  label="Bytecode 1"
                  value={bytecodeOne}
                  onChange={(e) => setBytecodeOne(e.target.value)}
                />
                <BytecodeLengthDisplay
                  bytecode={bytecodeOne}
                  label="Bytecode"
                />
              </div>
              <div className="flex flex-col">
                <AppTextarea
                  className="mb-[8px]"
                  label="Bytecode 2"
                  value={bytecodeTwo}
                  onChange={(e) => setBytecodeTwo(e.target.value)}
                />
                <BytecodeLengthDisplay
                  bytecode={bytecodeTwo}
                  label="Bytecode"
                />
              </div>
            </div>

            <div className="mb-4 flex items-center gap-4">
              <label className="flex items-center gap-2 text-fore-neutral-secondary">
                <input
                  type="checkbox"
                  checked={ignoreHash}
                  onChange={(e) => setIgnoreHash(e.target.checked)}
                  className="size-4"
                />
                Ignore hash
              </label>

              <label className="flex items-center gap-2 text-fore-neutral-secondary">
                <input
                  type="checkbox"
                  checked={ignoreCbor}
                  onChange={(e) => setIgnoreCbor(e.target.checked)}
                  className="size-4"
                />
                Ignore cbor
              </label>
            </div>

            <AppButton onClick={handleCompare} disabled={isComparing}>
              {isComparing ? "Comparing..." : "Compare"}
            </AppButton>

            {comparisonResult && (
              <div className="mt-6 rounded-md bg-back-neutral-tertiary p-4 text-fore-neutral-primary">
                <H2 className="mb-4">Comparison Results</H2>

                <div className="mb-6">
                  <ComparisonStatus
                    isIdentical={comparisonResult.isIdentical}
                  />
                </div>

                {(comparisonResult.replacedSections.file1.length > 0 ||
                  comparisonResult.replacedSections.file2.length > 0) && (
                  <div className="mb-6">
                    <H3 className="mb-2">Replaced sections for comparison:</H3>

                    <ReplacedSections
                      sections={comparisonResult.replacedSections.file1}
                      fileNumber={1}
                    />
                    <ReplacedSections
                      sections={comparisonResult.replacedSections.file2}
                      fileNumber={2}
                    />
                  </div>
                )}

                {!comparisonResult.isIdentical &&
                  comparisonResult.differences.length > 0 && (
                    <div className="mb-6">
                      <H3 className="mb-2">
                        Found {comparisonResult.differences.length} difference
                        {comparisonResult.differences.length !== 1 ? "s" : ""}:
                      </H3>

                      {comparisonResult.differences.map((diff, index) => (
                        <div
                          key={`diff-${diff.start}-${diff.end}`}
                          className="mb-4 rounded bg-back-neutral-secondary p-3"
                        >
                          <H4>Difference #{index + 1}:</H4>
                          <Body2>
                            Position: {diff.start} to {diff.end} (length:{" "}
                            {diff.end - diff.start + 1})
                          </Body2>
                          <div className="mt-2">
                            <Body2>
                              File 1: {diff.content1 || "(missing)"}
                            </Body2>
                            <Body2>
                              File 2: {diff.content2 || "(missing)"}
                            </Body2>
                          </div>

                          <div className="mt-2">
                            <H5>Context:</H5>
                            <div className="overflow-x-auto">
                              <pre className="text-xs">
                                File 1:{" "}
                                {diff.context1.substring(
                                  0,
                                  diff.start -
                                    (diff.start - 16 > 0 ? diff.start - 16 : 0)
                                )}
                                <span className="bg-status-error/20 px-1">
                                  {diff.content1}
                                </span>
                                {diff.context1.substring(diff.content1.length)}
                              </pre>
                              <pre className="text-xs">
                                File 2:{" "}
                                {diff.context2.substring(
                                  0,
                                  diff.start -
                                    (diff.start - 16 > 0 ? diff.start - 16 : 0)
                                )}
                                <span className="bg-status-success/20 px-1">
                                  {diff.content2}
                                </span>
                                {diff.context2.substring(diff.content2.length)}
                              </pre>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                {!comparisonResult.isIdentical && (
                  <div>
                    <H3>Summary:</H3>
                    <Body2>
                      {comparisonResult.summary.totalDiffLength} different
                      characters ({comparisonResult.summary.percentDiff}% of the
                      analyzed bytecode)
                    </Body2>
                  </div>
                )}
              </div>
            )}
          </div>
        </GradientWrapper>
      </div>
    </div>
  );
}
