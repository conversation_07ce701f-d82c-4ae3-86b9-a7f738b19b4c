import Link from "next/link";
import { useContext } from "react";
import { GoGear } from "react-icons/go";
import { IoMdDownload } from "react-icons/io";

import { AppButton } from "@/app/components/app-button";
import { AppSelect } from "@/app/components/app-select";
import { ReconMode } from "@/app/services/generateFuzzerContracts";

import { ABIContext } from "./abi-context";

const reconModeOptions = [
  { value: ReconMode.normal, label: "Normal mode" },
  { value: ReconMode.fail, label: "Fail mode" },
  { value: ReconMode.catch, label: "Catch mode" },
];

export const ABIActionsRow = () => {
  const { downloadAllContracts, setReconMode, reconMode } =
    useContext(ABIContext);

  const handleModeChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setReconMode(event.target.value as ReconMode);
  };

  return (
    <div className="mb-[21px] flex items-center justify-between">
      <div className="ml-auto flex gap-[27px]">
        <Link href="/installation-help">
          <AppButton
            className="w-[190px]"
            leftIcon={<GoGear />}
            variant="primary"
          >
            Installation Help
          </AppButton>
        </Link>
        <AppButton
          variant="primary"
          leftIcon={<IoMdDownload />}
          onClick={downloadAllContracts}
        >
          Download all files
        </AppButton>

        <AppSelect
          value={reconMode}
          onChange={handleModeChange}
          options={reconModeOptions}
          containerClassName="w-[210px]"
        />
      </div>
    </div>
  );
};
