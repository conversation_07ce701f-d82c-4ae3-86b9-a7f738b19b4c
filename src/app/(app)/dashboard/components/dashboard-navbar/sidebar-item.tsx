import noop from "lodash/noop";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { signOut } from "next-auth/react";
import type { IconType } from "react-icons/lib";
import {
  MdOutlineKeyboardArrowDown,
  MdOutlineKeyboardArrowUp,
} from "react-icons/md";

import { pathMatch } from "@/app/helpers";
import { cn } from "@/app/helpers/cn";
import { useBooleanState } from "@/app/services/helpers";
import { AppSpinner } from "../../../../components/app-spinner";

export interface ABIChildItem {
  label: string;
  icon: IconType;
  href: string;
}

export interface SidebarItemProps {
  icon: IconType;
  label: string;
  childItems?: ABIChildItem[];
  isChildItemsLoading?: boolean;
  href?: string;
  newTab?: boolean;
  type?: "link" | "button" | "logout";
}

export const SidebarItem = ({
  icon: Icon,
  label,
  childItems,
  isChildItemsLoading,
  href,
  newTab,
  type,
}: SidebarItemProps) => {
  const [childOpen, { toggle: onToggleChildren }] = useBooleanState(false);
  const pathname = usePathname();

  const handleLogout = async () => {
    await signOut({ callbackUrl: "/" });
  };

  const isActive = pathMatch(pathname, href) && (!childItems || !childOpen);
  const hasChildItems = Array.isArray(childItems) && childItems.length > 0;

  const itemClasses = cn(
    "flex items-center py-[13px] pl-[28px] gap-[8px] text-[16px] hyphens-auto w-full justify-start pr-[24px] overflow-ellipsis whitespace-nowrap transition-colors",
    {
      // Active state - purple background with white text
      "bg-accent-primary text-fore-on-accent-primary":
        isActive && !hasChildItems,
      // Parent item with open children - subtle accent background with purple text
      "bg-back-accent-secondary text-accent-primary":
        hasChildItems && childOpen,
      // Default state - neutral text color (white/black based on theme)
      "text-fore-neutral-primary": !isActive && !childOpen,
    }
  );

  const childItemClasses = (isActive: boolean) =>
    cn(
      "py-[13px] pl-[28px] text-[16px] flex w-[100%] items-center gap-[8px] truncate transition-colors",
      {
        // Active child - purple background with white text
        "bg-accent-primary text-fore-on-accent-primary": isActive,
        // Inactive child - subtle background
        "bg-back-accent-tertiary text-fore-neutral-primary": !isActive,
      }
    );

  const wrapInLink = (children: React.ReactNode) =>
    href ? (
      <Link href={href} target={newTab ? "_blank" : undefined}>
        {children}
      </Link>
    ) : (
      children
    );

  const ChevronIcon = childOpen
    ? MdOutlineKeyboardArrowDown
    : MdOutlineKeyboardArrowUp;

  const getClickHandler = () => {
    if (type === "logout") return handleLogout;
    if (hasChildItems) return onToggleChildren;
    return noop;
  };

  const item = (
    <button
      className={itemClasses}
      onClick={getClickHandler()}
      aria-label={label}
    >
      <Icon className="w-[20px]" />
      {label}
      {hasChildItems && <ChevronIcon className="ml-auto size-[15px]" />}
    </button>
  );

  return (
    <li>
      {type === "link" ? wrapInLink(item) : item}
      {hasChildItems &&
        childOpen &&
        (isChildItemsLoading ? (
          <div className="flex h-[58px] items-center justify-center">
            <AppSpinner />
          </div>
        ) : (
          <div className="bg-back-accent-secondary">
            {childItems.map(({ href, label, icon: Icon }) => (
              <div
                className={childItemClasses(pathMatch(pathname, href))}
                key={label}
              >
                <Icon className="w-[20px]" />
                <Link
                  href={href}
                  title={label}
                  key={href}
                  className="w-[230px] truncate"
                >
                  {label}
                </Link>
              </div>
            ))}
          </div>
        ))}
    </li>
  );
};
