"use client";

import React from "react";
import Link from "next/link";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>hield, <PERSON>Zap } from "react-icons/fi";
import {
  H2,
  Title3Strong,
  Body4,
  Body4Strong,
  AttributionStrong,
} from "../../../components/app-typography";
import { AppButton } from "../../../components/app-button";
import { FreeIcon } from "./dashboard-plan-free-icon";
import { ProIcon } from "./dashboard-plan-pro-icon";

// Declarative plan configs
const FREE_PLAN: PlanCardProps = {
  title: "Free (Your account)",
  description:
    "Recon Free for Public Goods. Public repositories can build handlers and update ABI data on commits.",
  features: [
    "Recon Free for Public Goods",
    "Public Repositories can build handlers for Free",
    "Update ABI Data on new commits",
    "Automatically re-build handlers",
    "Export files and run them locally",
  ],
  icon: <FreeIcon />,
  isHighlighted: false,
  buttonText: "Current Plan",
};

const PRO_PLAN: PlanCardProps = {
  title: "Pro version",
  description:
    "Pro organizations can build both Public and Private Repos and much more",
  features: [
    "Add Public and Private Repos to your organization and users",
    "Run multiple fuzzers in the cloud",
    "Automated Runs on PR commit",
    "Automated broken invariants test generation",
    "Advanced builder",
    "Store recipes of your common jobs",
    "One click shareable job reports for your SRs",
    "Private coaching",
  ],
  icon: <ProIcon />,
  isHighlighted: true,
  price: "$XX",
  priceSubtext: "per month",
  buttonText: "Go pro now",
  buttonHref: "/pro",
  badge: "Best offer",
};

const PLANS: PlanCardProps[] = [FREE_PLAN, PRO_PLAN];

interface PlanCardProps {
  title: string;
  description: string;
  features: string[];
  icon: React.ReactNode;
  isHighlighted?: boolean;
  price?: string;
  priceSubtext?: string;
  buttonText: string;
  buttonHref?: string;
  badge?: string;
}

const PlanCard: React.FC<PlanCardProps> = ({
  title,
  description,
  features,
  icon,
  isHighlighted = false,
  price,
  priceSubtext,
  buttonText,
  buttonHref,
  badge,
}) => {
  const textColor = isHighlighted
    ? "text-back-neutral-primary"
    : "text-fore-neutral-primary";

  const cardContent = (
    <div
      className={`flex h-full flex-col rounded-xl border p-5 transition-all duration-200 ${
        isHighlighted
          ? "border-accent-primary bg-accent-primary text-back-neutral-primary"
          : "hover:border-accent-primary/50 border-fore-neutral-quaternary bg-accent-alt-tertiary text-fore-neutral-primary"
      }`}
    >
      <div className="mb-7 flex flex-col gap-3">
        <div className="flex items-start justify-between">
          <div className="flex flex-col gap-3">
            {icon}

            <div className="flex flex-col gap-1">
              <div className="flex items-center gap-4">
                <H2 className={textColor}>{title}</H2>
                {badge && (
                  <div className="rounded-lg border border-[#7160E8] px-1.5 py-1">
                    <AttributionStrong className="!text-[#7160E8]">
                      {badge}
                    </AttributionStrong>
                  </div>
                )}
              </div>
              <Body4Strong className={textColor}>{description}</Body4Strong>
            </div>
          </div>
        </div>

        <div className={`mb-3 h-px ${"bg-fore-neutral-quaternary"}`} />

        {price && (
          <div className="flex items-end gap-1">
            <span className={`text-5xl font-bold leading-none ${textColor}`}>
              {price}
            </span>
            {priceSubtext && (
              <Body4Strong className={textColor}>{priceSubtext}</Body4Strong>
            )}
          </div>
        )}
      </div>

      <div className="mb-6 flex flex-1 flex-col gap-3">
        {features.map((feature) => (
          <div key={feature} className="flex items-start gap-4">
            <div className="mt-0.5 flex size-6 items-center justify-center rounded-full">
              <FiCheck size={16} className={textColor} />
            </div>
            <Body4 className={textColor}>{feature}</Body4>
          </div>
        ))}
      </div>

      <AppButton
        variant={isHighlighted ? "primary" : "outline"}
        size="lg"
        fullWidth
      >
        {buttonText}
      </AppButton>
    </div>
  );

  if (buttonHref) {
    return (
      <Link href={buttonHref} className="flex h-full">
        {cardContent}
      </Link>
    );
  }

  return cardContent;
};

const DashboardPlan: React.FC = () => {
  return (
    <div className="rounded-xl bg-back-neutral-secondary p-10">
      <div className="mb-8 flex flex-col items-center gap-3.5 text-center">
        <H2 className="max-w-[688px] text-fore-neutral-primary">
          Find Your Perfect Plan
        </H2>
        <Title3Strong className="max-w-[658px] text-fore-neutral-secondary">
          Discover the ideal plan to fuel your organization growth. Our pricing
          options are carefully crafted to cater to businesses.
        </Title3Strong>
      </div>

      <div className="grid grid-cols-1 gap-10 lg:grid-cols-2">
        {PLANS.map((plan) => (
          <PlanCard key={plan.title} {...plan} />
        ))}
      </div>
    </div>
  );
};

export default DashboardPlan;
