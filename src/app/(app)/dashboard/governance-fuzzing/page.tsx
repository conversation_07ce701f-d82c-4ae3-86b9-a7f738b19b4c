"use client";

import { AppButton } from "@/app/components/app-button";
import { AppCode } from "@/app/components/app-code";
import { AppInput } from "@/app/components/app-input";
import { AppInputDropdown } from "@/app/components/app-input-dropdown";
import { H2, Body3 } from "@/app/components/app-typography";
import { useGetGovernanceFuzzing } from "@/app/services/governanceFuzzing.hook";
import { useGetRecipes } from "@/app/services/recipes.hook";
import { chains } from "@/lib/utils";
import axios from "axios";
import { ethers } from "ethers";
import { useEffect, useState } from "react";
import { FormProvider, useFieldArray, useForm } from "react-hook-form";
import { Toaster, toast } from "react-hot-toast";
import { AppPageHeader } from "../../components/app-page-header";
export type GitHubLinkFormValues = {
  contractAddress: string;
  chainId: number;
  recipeId: string;
  eventName: string;
  parameters: Array<{
    type: string;
    isIndexed: boolean;
    replacement?: string;
    unused: boolean;
  }>;
};

// ADD check for for fork replay ? (vm warp and roll )

export default function GovernanceFuzzing() {
  const methods = useForm<GitHubLinkFormValues>({
    defaultValues: {
      contractAddress: "",
      chainId: 1,
      recipeId: "",
      eventName: "",
      parameters: [],
    },
  });

  const { register, setValue, handleSubmit, watch, formState } = methods;
  const { fields, append, remove } = useFieldArray({
    control: methods.control,
    name: "parameters",
  });
  const { data: recipes, isLoading } = useGetRecipes();
  const { data: governanceFuzzing } = useGetGovernanceFuzzing();
  const [showEventDefinition, setShowEventDefinition] = useState(false);
  const [topic, setTopic] = useState<string>("");
  const [eventDefinition, setEventDefinition] = useState<string>("");
  const [isEditing, setIsEditing] = useState<Map<string, boolean>>(new Map());
  const eventName = watch("eventName");
  const parameters = watch("parameters");
  const [editForm, setEditForm] = useState<{
    address: string;
    chainId: string;
    eventDefinition: string;
    topic: string;
    prepareContracts: Array<{ target: string; replacement: string }>;
    id: string;
  } | null>(null);

  useEffect(() => {
    if (eventName && parameters?.length > 0) {
      const eventDefConstructedForTopic = `${eventName}(${parameters
        .map((param) =>
          param.isIndexed
            ? `${param.type.trim().toLowerCase()}`
            : param.type.trim().toLowerCase()
        )
        .join(",")})`;
      setTopic(
        ethers.keccak256(ethers.toUtf8Bytes(eventDefConstructedForTopic))
      );
      const eventDefConstructed = `${eventName}(${parameters
        .map((param) =>
          param.isIndexed
            ? `${param.type.trim().toLowerCase()} indexed`
            : param.type.trim().toLowerCase()
        )
        .join(",")})`;
      setEventDefinition(eventDefConstructed);
    }
  }, [eventName, parameters, formState]);

  const onSubmit = async (data: GitHubLinkFormValues) => {
    // Construct event definition
    const eventDefConstructedForTopic = `${data.eventName}(${data.parameters
      .map((param) =>
        param.isIndexed
          ? `${param.type.trim().toLowerCase()}`
          : param.type.trim().toLowerCase()
      )
      .join(",")})`;

    const formattedEventToTopics = ethers.keccak256(
      ethers.toUtf8Bytes(eventDefConstructedForTopic)
    );

    const eventDefConstructed = `${data.eventName}(${data.parameters
      .map((param) =>
        param.isIndexed
          ? `${param.type.trim().toLowerCase()} indexed`
          : param.type.trim().toLowerCase()
      )
      .join(",")})`;
    if (
      !data.parameters.some((param) => param.replacement?.includes("XX")) &&
      data.parameters.some((param) => param.unused === false)
    ) {
      toast.error("No XX found in replacement");
      return;
    }
    const prepContract = data.parameters.map((param, index) => {
      // Split the string by XX and join all but the last part

      const parts = param.replacement.split("XX");
      let lastPart = parts.pop(); // Remove and get the last part
      if (lastPart.endsWith(";")) {
        lastPart = lastPart.slice(0, -1);
      }
      const newReplacement = parts.join("XX") + `$_${index + 1}` + lastPart;

      return {
        target: `${param.replacement}`,
        replacement: `${newReplacement};`,
        endOfTargetMarker: "[^;]*",
        targetContract: "Setup.sol",
        unused: param.unused,
      };
    });

    console.log({
      contractAddress: data.contractAddress,
      recipeId: data.recipeId,
      topic: formattedEventToTopics,
      eventDefinition: eventDefConstructed,
      prepContract,
      chainId: data.chainId,
    });

    const response = await axios({
      method: "POST",
      url: `/api/governanceFuzzing`,
      data: {
        contractAddress: data.contractAddress,
        recipeId: data.recipeId,
        topic: formattedEventToTopics,
        eventDefinition: eventDefConstructed,
        prepContract: prepContract,
        chainId: data.chainId,
      },
    });
    if (response.status === 200) {
      toast.success("Governance fuzzing setup");
      // window.location.reload();
    } else {
      toast.error("Fail to create Governance Fuzzing");
    }
  };

  const handleDelete = async (id: string) => {
    const response = await axios({
      method: "POST",
      url: `/api/governanceFuzzing/delete`,
      data: {
        id: id,
      },
    });
    if (response.status === 200) {
      // reload the page
      window.location.reload();
    }
  };

  const handleToggle = async (id: string) => {
    const response = await axios({
      method: "POST",
      url: `/api/governanceFuzzing/toggle`,
      data: {
        id: id,
      },
    });
    if (response.status === 200) {
      window.location.reload();
    }
  };
  const contractAddress = watch("contractAddress");
  const chainId = watch("chainId");
  const recipeId = watch("recipeId");

  const handleEdit = async (id: string) => {
    setIsEditing(new Map(isEditing).set(id, true));
    const govFuzz = governanceFuzzing.find((g) => g.id === id);
    console.log(govFuzz);
    if (govFuzz) {
      setEditForm({
        address: govFuzz.address,
        chainId: govFuzz.chainId.toString(),
        eventDefinition: govFuzz.eventDefinition,
        topic: govFuzz.topic,
        prepareContracts:
          govFuzz.recipes[0]?.fuzzerArgs?.prepareContracts || [],
        id: govFuzz.id,
      });
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setEditForm((prev) => {
      if (!prev) return null;
      let newTopic = prev.topic;
      if (field === "eventDefinition") {
        newTopic = ethers.keccak256(ethers.toUtf8Bytes(value));
      }
      return {
        ...prev,
        [field]: value,
        topic: newTopic,
      };
    });
  };

  const handlePrepareContractChange = (
    index: number,
    field: string,
    value: string
  ) => {
    setEditForm((prev) => {
      if (!prev) return null;
      const newPrepareContracts = [...prev.prepareContracts];
      newPrepareContracts[index] = {
        ...newPrepareContracts[index],
        [field]: value,
      };

      return {
        ...prev,
        prepareContracts: newPrepareContracts,
      };
    });
  };

  const editValidate = async () => {
    console.log("editForm", editForm);
    const response = await axios({
      method: "POST",
      url: `/api/governanceFuzzing/put`,
      data: editForm,
    });
    if (response.status === 200) {
      toast.success("Governance fuzzing updated");
      window.location.reload();
    } else {
      toast.error("Fail to update Governance Fuzzing");
    }
  };

  return (
    <div className="min-h-screen bg-back-neutral-secondary dark:bg-back-neutral-primary">
      <Toaster position="top-center" reverseOrder={false} />

      <div className="min-h-[65vh] overflow-auto p-[45px]">
        <AppPageHeader
          title="Governance Fuzzing"
          descriptions={[
            "Setup Event Listeners tied to your contract",
            "Please talk to staff to set these up if you need help",
          ]}
        />
        <FormProvider {...methods}>
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="grid grid-cols-1 gap-10 lg:grid-cols-[1fr_2fr]">
              <div className="w-full min-w-[200px] space-y-4 border-0 border-r-divider pr-0 lg:border-r lg:pr-[40px]">
                <div className="min-w-[200px]">
                  <AppInput
                    className="mb-[8px] min-w-[200px]"
                    label="Contract Address"
                    {...register("contractAddress", { required: true })}
                    type="text"
                  />
                </div>
                <div className="min-w-[200px]">
                  <AppInputDropdown
                    className="mb-[8px] min-w-[200px]"
                    label="Chain"
                    {...register("chainId")}
                    type="text"
                    dropdownItems={chains.map((chain) => ({
                      id: chain.id.toString(),
                      label: `${chain.id} - ${chain.name}`,
                      fields: chain,
                    }))}
                    onItemSelect={(id) => setValue("chainId", Number(id))}
                  />
                </div>
                {recipes && recipes.length > 0 && !isLoading ? (
                  <div className="min-w-[200px]">
                    <AppInputDropdown
                      className="mb-[8px] w-full min-w-[200px]"
                      {...register("recipeId", { required: true })}
                      type="text"
                      label="Recipe"
                      placeholder="Search by Name, Id, repo ..."
                      dropdownItems={recipes.map((rec) => ({
                        id: rec.id,
                        label: `${rec.displayName}`,
                        fields: rec,
                      }))}
                      onItemSelect={(id) => setValue("recipeId", id as string)}
                    />
                  </div>
                ) : isLoading ? (
                  <Body3 color="primary">Loading recipes ...</Body3>
                ) : (
                  <Body3 color="primary">No recipes found</Body3>
                )}

                {contractAddress && chainId && recipeId && (
                  <div className="mt-6">
                    {!showEventDefinition ? (
                      <AppButton
                        type="button"
                        onClick={() => setShowEventDefinition(true)}
                        className="mb-4"
                      >
                        Define New Event
                      </AppButton>
                    ) : (
                      <>
                        <AppInput
                          className="mb-[8px]"
                          label="Event Name"
                          placeholder="e.g. AddNumber"
                          {...register("eventName", { required: true })}
                          type="text"
                        />

                        <H2 className="mb-[16px]" color="primary">
                          Event Parameters ( MUST match event definition )
                        </H2>

                        {fields.map((field, index) => (
                          <div
                            key={field.id}
                            className="relative mb-[16px] rounded p-4"
                          >
                            <div className="mb-4 flex items-center gap-4">
                              <AppInput
                                className="flex-1"
                                label="Parameter Type"
                                placeholder="e.g. address, uint256"
                                {...register(`parameters.${index}.type`, {
                                  required: true,
                                })}
                                type="text"
                              />
                              <Body3
                                as="div"
                                className="flex items-center gap-2"
                                color="secondary"
                              >
                                <input
                                  type="checkbox"
                                  {...register(`parameters.${index}.isIndexed`)}
                                  className="size-4"
                                />
                                Indexed
                              </Body3>
                            </div>

                            <div className="flex flex-col">
                              <AppInput
                                label="Replacement"
                                {...register(`parameters.${index}.replacement`)}
                                type="text"
                                placeholder='bytes constant PAYLOAD = bytes(hex"XX")'
                              />
                              <Body3
                                as="div"
                                className="flex items-center gap-2"
                                color="secondary"
                              >
                                <input
                                  type="checkbox"
                                  {...register(`parameters.${index}.unused`)}
                                  className="size-4"
                                />
                                Unused
                              </Body3>
                              <Body3 color="secondary">
                                Ex: <pre>uint8 DECIMALS = uint8(XX)</pre>
                                Define the replacement in your contract. Not
                                that you need to input XX to allow us to replace
                                XX with the value from the event
                              </Body3>
                            </div>

                            <AppButton
                              type="button"
                              onClick={() => remove(index)}
                              className="absolute right-2 top-2 text-status-error"
                              variant="secondary"
                              size="sm"
                            >
                              Remove
                            </AppButton>
                          </div>
                        ))}

                        <AppButton
                          type="button"
                          onClick={() =>
                            append({
                              type: "",
                              isIndexed: false,
                              replacement: "",
                              unused: false,
                            })
                          }
                          className="mt-4"
                        >
                          Add Parameter
                        </AppButton>
                      </>
                    )}
                  </div>
                )}
              </div>
            </div>
            {topic && eventDefinition ? (
              <div>
                <Body3 color="primary">Topic: {topic}</Body3>
                <Body3 color="primary">
                  Event Definition: {eventDefinition}
                </Body3>
                <Body3 color="primary">
                  Make sure you verify this topic is correct before submitting
                </Body3>
                <Body3 color="primary">
                  Incorrect topic will make us miss events on chain
                </Body3>
              </div>
            ) : (
              <Body3 color="primary">No topic</Body3>
            )}

            {showEventDefinition && (
              <AppButton type="submit" className="mt-4">
                Submit
              </AppButton>
            )}
          </form>
        </FormProvider>
      </div>

      {governanceFuzzing?.length > 0 && (
        <div className="flex flex-col gap-[20px] p-[45px]">
          <H2 className="mb-[28px]" color="primary">
            Existing Governance Fuzzing
          </H2>
          {governanceFuzzing.map((govFuzz) => (
            <>
              {isEditing.get(govFuzz.id) && editForm && (
                <div className="flex flex-col gap-[20px] rounded-lg border border-stroke-neutral-decorative p-[20px]">
                  <AppInput
                    label="Address"
                    type="text"
                    value={editForm.address}
                    onChange={(e) =>
                      handleInputChange("address", e.target.value)
                    }
                  />
                  <AppInput
                    label="Chain Id"
                    type="text"
                    value={editForm.chainId}
                    onChange={(e) =>
                      handleInputChange("chainId", e.target.value)
                    }
                  />
                  <Body3 color="primary" className="font-semibold">
                    Prepare Contracts
                  </Body3>
                  {editForm.prepareContracts.map((contract, index) => (
                    <AppInput
                      key={`contract-${contract.target}-${index}`}
                      type="text"
                      value={contract.target}
                      placeholder="Contract address"
                      onChange={(e) =>
                        handlePrepareContractChange(
                          index,
                          "target",
                          e.target.value
                        )
                      }
                    />
                  ))}
                  <AppInput
                    label="Event Definition"
                    type="text"
                    value={editForm.eventDefinition}
                    onChange={(e) =>
                      handleInputChange("eventDefinition", e.target.value)
                    }
                  />
                  <AppInput
                    label="Topic"
                    type="text"
                    value={editForm.topic}
                    onChange={(e) =>
                      handleInputChange(
                        "topic",
                        ethers.keccak256(
                          ethers.toUtf8Bytes(editForm.eventDefinition)
                        )
                      )
                    }
                    disabled
                  />
                  <div className="flex gap-4">
                    <AppButton
                      type="button"
                      onClick={editValidate}
                      className="mt-4"
                    >
                      Validate edit
                    </AppButton>

                    <AppButton
                      type="button"
                      onClick={() =>
                        setIsEditing(new Map(isEditing).set(govFuzz.id, false))
                      }
                      className="mt-4"
                    >
                      Cancel edit
                    </AppButton>
                  </div>
                </div>
              )}
              {!isEditing.get(govFuzz.id) && (
                <div className="text-[18px] leading-[21px] text-fore-neutral-primary">
                  <div className="my-3 flex justify-between"></div>
                  <AppCode
                    code={JSON.stringify(govFuzz, null, 2)}
                    language="json"
                  />
                  <div className="flex w-full justify-between">
                    <AppButton
                      type="button"
                      onClick={() => handleDelete(govFuzz.id)}
                    >
                      Delete
                    </AppButton>
                    <AppButton
                      type="button"
                      onClick={() => handleToggle(govFuzz.id)}
                    >
                      Toggle Gov. Fuzzing
                    </AppButton>
                    <AppButton
                      type="button"
                      onClick={() => handleEdit(govFuzz.id)}
                    >
                      Edit
                    </AppButton>
                  </div>
                </div>
              )}
            </>
          ))}
        </div>
      )}
    </div>
  );
}
