"use client";

import { AppButton } from "@/app/components/app-button";
import { AppCode } from "@/app/components/app-code";
import { AppSpinner } from "@/app/components/app-spinner";
import { useGetMonitoring } from "@/app/services/monitoring.hook";
import { AppPageHeader } from "../../components/app-page-header";

export default function Monitoring() {
  const { data, isRefetching, isLoading, refetch } = useGetMonitoring();

  return (
    <div className="pl-[45px] pt-[45px]">
      <AppPageHeader
        title="Monitoring"
        descriptions={[
          "Live Monitoring",
          "This feature is manually enabled by the Recon Team. Checks are done each block",
          "Talk to staff to set these up!",
        ]}
      />

      <div className="text-fore-neutral-primary">
        {data?.map((callResponse) => (
          <div key={callResponse.id}>
            <h2>ID: {callResponse.id}</h2>
            <p>STATUS: {callResponse.status}</p>
            <AppButton onClick={refetch} disabled={isRefetching || isLoading}>
              {isRefetching || isLoading ? <AppSpinner /> : "Reload"}
            </AppButton>
            <div>
              <AppCode
                code={JSON.stringify({ ...callResponse.data.data }, null, 2)}
                // code={JSON.stringify({...callResponse.data.data, alerts: callResponse.data.alerts}, null, 2)}
                language="json"
              ></AppCode>
              {/* <h3>Properties</h3>
            <p>Checked at Block: {callResponse.data.data.block}</p>
            {Object.keys(callResponse.data.data.properties).map(key => (
              <div key={key}>
                Key: {key}
                Value: {callResponse.data.data.properties[key] ? "True" : "False"}
              </div>
            ))} */}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
