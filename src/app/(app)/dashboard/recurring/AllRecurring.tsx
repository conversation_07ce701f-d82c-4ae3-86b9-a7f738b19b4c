"use client";
import axios from "axios";
import Link from "next/link";

import { AppCode } from "@/app/components/app-code";
import { AppSpinner } from "@/app/components/app-spinner";
import { AppButton } from "@/app/components/app-button";
import { cn } from "@/app/helpers/cn";
import { useGetRecurring } from "@/app/services/recurring.hook";

export const AllRecurring = () => {
  const { data, isLoading, refetch, isRefetching } = useGetRecurring();
  const buttonDisabled = isLoading || isRefetching;

  const toggleRecurringJobHandler = async (recurringId: string) => {
    const updatedRecurringJob = await axios({
      method: "POST",
      url: `/api/recurringToggle`,
      data: {
        recurringId,
      },
    });
    if (updatedRecurringJob.status === 200) {
      refetch();
    } else {
      alert("something went wrong");
    }
  };

  return (
    <div className="mb-[20px] mt-[50px] pr-[45px]">
      <div className="mb-[40px] flex items-center justify-between">
        <AppButton onClick={refetch} disabled={buttonDisabled}>
          {buttonDisabled ? <AppSpinner /> : "Force Reload"}
        </AppButton>
      </div>

      {data?.length > 0 && (
        <div className="flex flex-col gap-[20px]">
          {data.map((cronInfo, index) => (
            <div
              key={cronInfo.id}
              className={cn(
                "py-[20px] px-[22px] justify-between bg-back-neutral-tertiary gradient-dark-bg rounded-t-[8px] border-stroke-neutral-decorative border border-b-0 rounded-b-[8px] border-b-1"
              )}
            >
              <div className="text-[18px] leading-[21px] text-fore-neutral-primary">
                <div className="flex justify-between">
                  <p>{cronInfo.label}</p>
                </div>
                <AppCode
                  code={JSON.stringify(cronInfo, null, 2)}
                  language="json"
                />
              </div>
              <div className="flex w-full flex-row justify-between">
                <Link href={`/dashboard/recurring/${cronInfo.id}`}>
                  <AppButton>View Latest Runs</AppButton>
                </Link>
                <AppButton
                  onClick={() => toggleRecurringJobHandler(cronInfo.id)}
                >
                  {cronInfo.enabled ? "Disable" : "Enable"} recurring job
                </AppButton>
                <Link href={`/dashboard/recurring/${cronInfo.id}/manage`}>
                  <AppButton>Manage alerts</AppButton>
                </Link>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};
