"use client";


import { AppPageHeader } from "../../components/app-page-header";
import { AllCampaigns } from "./AllCampaigns";
import { CreateCampaign } from "./CreateCampaign";

// Create a Campaign
// -> orgName, repoName, ref
// List all Campaigns

export default function Campaigns() {
  return (
    <div className="pl-[45px] pt-[45px]">
      <AppPageHeader
        title="Campaigns"
        descriptions={[
          "Create a campaign to automatically run jobs on PR Push and Creation",
          "• Create a Recipe",
          "• Create a Campaign",
          "Once you have created a Recipe, you can create a Campaign to re-use the recipe on changes. All fields from a Pull Request are ORd with the Recipe data. Meaning you can re-use the same recipe with different repositories",
        ]}
      />

      <div>
        <CreateCampaign />
        <AllCampaigns />
      </div>
    </div>
  );
}
