import axios from "axios";
import { useState } from "react";
import { FormProvider, useForm } from "react-hook-form";

import { ENV_TYPE } from "@/app/app.constants";
import { useGetCampaigns } from "@/app/services/campaigns.hook";
import { useGetRecipes } from "@/app/services/recipes.hook";

import { AppButton } from "../../../components/app-button";
import { AppInput } from "../../../components/app-input";
import { AppRadioGroup } from "../../../components/app-radio-group";
import { AppSpinner } from "../../../components/app-spinner";
import { SubFormEchidna } from "../../../components/create-job-form/subform-echidna";
import { SubFormMedusa } from "../../../components/create-job-form/subform-medusa";
import Link from "next/link";
import { AppInputDropdown } from "@/app/components/app-input-dropdown";

type GitHubLinkInputProps = {
  title?: string;
  submitLabel?: string;
  hidePresets?: boolean;
};

// TODO
/**
 * displayName, orgNames, repoNames, branchNames, recipeIds
 *
 */
export type CampaignCreationFormValues = {
  // UX
  githubURL: string;

  // Data
  displayName: string;
  orgName: string;
  repoName: string;
  branchName: string;
  recipeId: string;
};

// TODO: On Commit, On PR + Commit (TODO: Issue with double firing of events)
// const radioOptions = [
//   {
//     label: "Medusa",
//     value: "MEDUSA",
//   },
//   {
//     label: "Echidna",
//     value: "ECHIDNA",
//   },
// ];

export function CreateCampaign({
  title = "Create Campaign",
  submitLabel = "Create Campaign",
}: GitHubLinkInputProps) {
  const {
    register,
    handleSubmit,
    setValue,
    setError,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<CampaignCreationFormValues>();

  const { refetch: refetchCampaigns } = useGetCampaigns();
  const { data: recipes, isLoading: isLoadingRecipes } = useGetRecipes();

  const onSubmit = async ({
    displayName,
    orgName,
    repoName,
    branchName,
    recipeId,
  }: CampaignCreationFormValues) => {
    // TODO: We need to pass them as Array of Values
    if (recipes.filter((el) => el.id === recipeId).length === 0) {
      alert("Invalid Recipe ID");
      return;
    }
    try {
      const foundData = await axios({
        method: "POST",
        url: `/api/campaigns`,
        data: {
          displayName, // Can be empty string
          orgNames: [orgName],
          repoNames: [repoName],
          branchNames: [branchName],
          recipeIds: [recipeId],
        },
      });
      refetchCampaigns();
    } catch (e) {
      console.log("e", e);
      alert(`Something went wrong: ${e.response.data.message}`);
    }
  };

  const parseURI = (inputValue) => {
    const success = /^(https?:\/\/)?(www\.)?github\.com\/.+\/.+(\.git)?$/.test(
      inputValue
    );

    if (!success) {
      setError("githubURL", {
        message: "Invalid GitHub URL",
      });
      return;
    }

    const ghLink = inputValue.endsWith("/")
      ? inputValue.slice(0, -1)
      : inputValue;
    const uriParts = ghLink
      .replace("https://", "")
      .replace("http://", "")
      .split("/");

    if (uriParts.length >= 3) {
      const orgName = uriParts[1];
      const repoName = uriParts[2];
      let branchName = "main"; // Default branch

      // Check if the URL specifies a branch
      if (uriParts.length > 5 && uriParts[3] === "tree") {
        // The branch name can include slashes, so we join the remaining parts
        branchName = uriParts.slice(4).join("/");
      } else if (uriParts.length === 5 && uriParts[3] === "tree") {
        // Handle the case where there's no slash in the branch name
        branchName = uriParts[4];
      }

      // Set the values to the form
      setValue("orgName", orgName);
      setValue("repoName", repoName);
      setValue("branchName", branchName);
    }
  };

  const githubUrlRegister = register("githubURL");
  return (
    <FormProvider
      {...({
        register,
        handleSubmit,
        setValue,
        setError,
        watch,
        errors,
        isSubmitting,
      } as any)}
    >
      <form onSubmit={handleSubmit(onSubmit)}>
        <h3 className="my-[22px] text-[28px] leading-[33px] text-fore-neutral-primary">
          {title}
        </h3>

        {/* <div className="mb-[22px] w-[450px]">
          <p className="text-fore-neutral-secondary mb-[9px] text-[15px] leading-[18px]">
            Select Job Type
          </p>
          {env && (
            <AppRadioGroup
              name="env"
              options={radioOptions}
              onChange={handleRadioChange}
              value={env}
            />
          )}
        </div> */}
        {recipes && recipes.length > 0 && !isLoadingRecipes ? (
          <div className="flex flex-wrap gap-[40px]">
            <div className="min-w-[450px] border border-y-0 border-l-0 border-r-divider pr-[40px]">
              <div className="min-w-[400px]">
                <AppInput
                  className="mb-[8px]"
                  label="Campaign Display Name"
                  {...register("displayName")}
                  type="text"
                />
                <AppInput
                  {...githubUrlRegister}
                  label="Paste Github URL for your convenience"
                  onChange={(e) => {
                    githubUrlRegister.onChange(e); // default react-hook-form onChange
                    parseURI(e.target.value); // additional logic for parsing URL
                  }}
                  type="text"
                  placeholder="Enter GitHub Repo URL"
                  error={errors.githubURL?.message}
                />

                <div className="my-[26px] h-px w-full bg-divider" />
              </div>

              <h3 className="mb-[16px] text-[16px] leading-[19px] text-fore-neutral-primary">
                Or specify the Organization, Repository and Branch directly
              </h3>

              <div className="">
                <AppInput
                  className="mb-[8px]"
                  label="Organization"
                  {...register("orgName")}
                  type="text"
                />

                <AppInput
                  className="mb-[8px]"
                  {...register("repoName")}
                  type="text"
                  label="Repo"
                />

                <AppInput
                  className="mb-[8px]"
                  {...register("branchName")}
                  type="text"
                  label="Branch"
                />
                <AppInputDropdown
                  className="mb-[8px] w-full"
                  {...register("recipeId")}
                  type="text"
                  label="Recipe"
                  placeholder="Search by Name, Id, repo ..."
                  dropdownItems={recipes.map((rec) => ({
                    id: rec.id,
                    label: `${rec.displayName}`,
                    fields: rec,
                  }))}
                  onItemSelect={(id) => setValue("recipeId", id as string)}
                />
              </div>
              <AppButton
                type="submit"
                disabled={isSubmitting}
                className="mb-[20px] mt-[35px] w-[233px] justify-center"
              >
                {isSubmitting ? <AppSpinner /> : submitLabel}
              </AppButton>
            </div>
          </div>
        ) : !isLoadingRecipes && (!recipes || recipes.length === 0) ? (
          <div className="text-fore-neutral-primary">
            <p>No recipes found.</p>
            <Link href="/dashboard/recipes/" className="cursor-pointer">
              <AppButton variant="primary">
                Please create a recipe first
              </AppButton>
            </Link>
          </div>
        ) : (
          <p className="text-fore-neutral-primary">Loading ...</p>
        )}
      </form>
    </FormProvider>
  );
}
