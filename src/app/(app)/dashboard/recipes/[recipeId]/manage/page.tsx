"use client";
import { use, useEffect, useState } from "react";
import { useGetRecipes } from "@/app/services/recipes.hook";
import AlertManager from "@/app/components/AlertManager/AlertManager";

export default function SingleCampaignPage({
  params,
}: {
  params: Promise<{ recipeId: string }>;
}) {
  const { recipeId } = use(params);
  const { data, refetch } = useGetRecipes();
  const [currentRecipe, setCurrentRecipe] = useState(null);

  useEffect(() => {
    if (recipeId && data) {
      const curr = data.find((recipe) => recipe.id === recipeId);
      setCurrentRecipe(curr);
    }
  }, [recipeId, data]);

  return (
    <AlertManager
      isRecipe={true}
      isRecurring={false}
      refetch={refetch}
      data={currentRecipe}
    />
  );
}
