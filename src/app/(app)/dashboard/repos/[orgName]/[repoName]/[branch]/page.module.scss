.section {
  padding: 24px;
}

.header {
  height: 100px;
}

.headerTitle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 5px;
  h2 {
    margin: 0;
  }
}

.abi {
  padding: 20px;
  margin-top: 24px;
  color: var(--fore-on-accent-primary);
  background-color: var(--back-neutral-secondary);
  border-radius: 6px;
  font-size: 15px;
}
.select {
  display: flex;
  flex-direction: column;
  gap: 4px;
  .label {
    font-size: 15px;
    font-weight: bold;
    color: var(--fore-neutral-quaternary);
  }
}

// New designs

.buttonGrid {
  display: grid;
  // 5 columns
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
}

.handlerButtonEnabled {
  font-size: 12px;
  background-color: var(--status-success);
}
.handlerButtonDisabled {
  font-size: 12px;
  background-color: var(--status-error);
}

.singleContract {
  margin-bottom: 50px;
}
