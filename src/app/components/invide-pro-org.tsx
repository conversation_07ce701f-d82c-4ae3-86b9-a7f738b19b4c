"use client";
import axios from "axios";
import { useState } from "react";

import { cn } from "../helpers/cn";
import { AppButton } from "./app-button";
import { AppListCheckItem } from "./app-list-check-item";
import { AppSpinner } from "./app-spinner";
import { H2, Body3 } from "./app-typography";

export const InviteInProOrg = () => {
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState("");
  const [data, setData] = useState(null);

  const createNewInvite = async (e) => {
    e.preventDefault();

    if (loading) {
      return;
    }
    setLoading(true);
    try {
      const foundData = await axios({
        method: "POST",
        url: `/api/orgInvites`,
      });

      setMessage(foundData?.data?.message);
      setData(foundData?.data?.data);
    } catch (e) {
      console.log("e", e);
      alert(`Something went wrong: ${e.response.data.message}`);
      setLoading(false);
    }
  };

  return (
    <div>
      <H2 className="mb-4" color="primary">
        Create a new invite
      </H2>

      <ul className="my-[10px]">
        {[
          " NOTE! Invite codes give access to your Repo and ABIS!! ",
          "Do not share them unless you want them to join the org!",
          "Each code is one time use!",
        ].map((text) => (
          <AppListCheckItem text={text} key={text} />
        ))}
      </ul>

      <AppButton
        onClick={createNewInvite}
        disabled={loading}
        className="my-[10px]"
      >
        {loading ? <AppSpinner /> : "Create new Code"}
      </AppButton>

      {message && (
        <div
          className={cn(
            "my-[20px] rounded-[5px] p-3 text-fore-neutral-primary",
            {
              "bg-status-error": !data,
              "bg-status-success": data,
              "bg-status-warning": !data && !loading,
            }
          )}
        >
          <Body3 color="primary">{message}</Body3>
        </div>
      )}
    </div>
  );
};
