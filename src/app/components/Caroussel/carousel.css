/* Custom Carousel Styles */

/* Hide default Swiper navigation buttons */
.swiper-button-next,
.swiper-button-prev {
  display: none !important;
}

/* Ensure our custom buttons work with Swiper */
.swiper-button-prev-custom,
.swiper-button-next-custom {
  cursor: pointer;
}

/* Disabled state for custom buttons */
.swiper-button-disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  pointer-events: none !important;
}

/* Ensure slides maintain proper spacing and width */
.swiper-slide {
  height: auto;
  display: flex;
  align-items: stretch;
  width: auto !important; /* Let content determine width */
  flex-shrink: 0;
}

/* Prevent overflow issues and ensure proper navigation */
.swiper-container {
  overflow: visible;
}

.swiper-wrapper {
  align-items: stretch;
}

/* Ensure navigation works properly with auto width */
.swiper {
  overflow: visible;
}

/* Fix navigation button states */
.swiper-button-prev-custom.swiper-button-disabled,
.swiper-button-next-custom.swiper-button-disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  pointer-events: none !important;
}
