"use client";

import React, { useState, useRef, useId } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import { FiChevronLeft, FiChevronRight } from "react-icons/fi";
import { cn } from "@/lib/utils";
import type { Swiper as SwiperType } from "swiper";

import "swiper/css";
import "swiper/css/navigation";
import "./carousel.css";

interface CarousselProps {
  data: any[];
  children: React.ReactNode;
  className?: string;
  spaceBetween?: number;
}

const CAROUSEL_CONFIG = {
  BUTTON_SIZE: 24,
} as const;

interface CarouselButtonProps {
  onClick: () => void;
  direction: "prev" | "next";
  className?: string;
  disabled?: boolean;
}

function CarouselButton({
  onClick,
  direction,
  className,
  disabled = false,
}: CarouselButtonProps) {
  const Icon = direction === "prev" ? FiChevronLeft : FiChevronRight;
  const positionClass = direction === "prev" ? "right-12" : "right-0";

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={cn(
        "absolute top-[-50px] z-10 rounded-full border border-accent-primary bg-back-neutral-secondary p-2 text-fore-on-accent-primary transition-all duration-200 hover:bg-accent-primary hover:scale-110",
        positionClass,
        {
          "opacity-50 cursor-not-allowed hover:scale-100 hover:bg-back-neutral-secondary":
            disabled,
        },
        className
      )}
      aria-label={`${direction === "prev" ? "Previous" : "Next"} item`}
    >
      <Icon size={CAROUSEL_CONFIG.BUTTON_SIZE} />
    </button>
  );
}

export default function Caroussel({
  data,
  children,
  className,
  spaceBetween = 16,
}: CarousselProps) {
  const [isBeginning, setIsBeginning] = useState(true);
  const [isEnd, setIsEnd] = useState(false);
  const swiperRef = useRef<SwiperType | null>(null);

  // Generate unique IDs for this carousel instance
  const uniqueId = useId();
  const prevButtonClass = `swiper-button-prev-custom-${uniqueId}`;
  const nextButtonClass = `swiper-button-next-custom-${uniqueId}`;

  const childrenArray = Array.isArray(children) ? children : [children];

  const handleSlideChange = (swiper: SwiperType) => {
    setIsBeginning(swiper.isBeginning);
    setIsEnd(swiper.isEnd);
  };

  return (
    <div
      className={cn(
        "relative flex w-full items-center justify-center",
        className
      )}
    >
      <CarouselButton
        onClick={() => {}}
        direction="prev"
        className={prevButtonClass}
        disabled={isBeginning}
      />
      <CarouselButton
        onClick={() => {}}
        direction="next"
        className={nextButtonClass}
        disabled={isEnd}
      />

      <div className="w-full">
        <Swiper
          id={uniqueId}
          modules={[Navigation]}
          spaceBetween={spaceBetween}
          slidesPerView="auto"
          navigation={{
            prevEl: `.${prevButtonClass}`,
            nextEl: `.${nextButtonClass}`,
            disabledClass: "swiper-button-disabled",
          }}
          watchSlidesProgress={true}
          watchOverflow={false}
          allowTouchMove={true}
          threshold={5}
          longSwipesRatio={0.1}
          slidesOffsetBefore={0}
          slidesOffsetAfter={0}
          normalizeSlideIndex={false}
          onSwiper={(swiper) => {
            swiperRef.current = swiper;
            handleSlideChange(swiper);
          }}
          onSlideChange={handleSlideChange}
          onReachBeginning={() => setIsBeginning(true)}
          onReachEnd={() => setIsEnd(true)}
          onFromEdge={() => {
            setIsBeginning(false);
            setIsEnd(false);
          }}
          className="w-full"
        >
          {data.map((item, index) => (
            <SwiperSlide
              key={`carousel-slide-${index}-${
                item?.id || item?.customer || index
              }`}
            >
              {childrenArray[index]}
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
  );
}
