import { forwardRef, useId, useState, useMemo, useEffect } from "react";
import type { IconType } from "react-icons/lib";
import { cn } from "../helpers/cn";
import { usePersistedTheme } from "../services/useThemePersistence";
import type { Recipe } from "../services/recipes.hook";
import type { AbiApiData } from "../types/abi.api";

type Chain = {
  id: number;
  name: string;
};

type DropdownItem = {
  id: string | number;
  label: string;
  fields: Recipe | AbiApiData | Chain;
};

type AppInputDropdownProps = {
  type?: "text" | "password";
  className?: string;
  defaultValue?: string;
  disabled?: boolean;
  onChange?: (e: any) => void;
  value?: string | number;
  placeholder?: string;
  label?: string;
  containerClassName?: string;
  error?: string;
  disableError?: boolean;
  icon?: IconType;
  dropdownItems?: DropdownItem[]; // Array of objects for dropdown
  onItemSelect?: (id: string | number) => void; // Function to get the selected item's ID
};

export const AppInputDropdown = forwardRef(
  (
    {
      type,
      onChange,
      defaultValue,
      disabled,
      value,
      placeholder = "",
      className = "",
      label,
      containerClassName = "",
      error,
      icon: Icon,
      disableError = false,
      dropdownItems = [],
      onItemSelect,
      ...rest
    }: AppInputDropdownProps,
    ref: any
  ) => {
    const id = useId();
    const { isDark } = usePersistedTheme();
    const [searchTerm, setSearchTerm] = useState("");
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);

    // Filter dropdown items based on the search term
    const filteredItems = useMemo(() => {
      return dropdownItems.filter((item) =>
        Object.values(item.fields).some((value) =>
          String(value).toLowerCase().includes(searchTerm)
        )
      );
    }, [dropdownItems, searchTerm]);

    const getInputStyles = () => {
      const baseStyles =
        "h-[40px] rounded-[6px] px-4 w-full min-w-[200px] outline-none transition-all duration-200 font-bold text-[16px] leading-[1.375]";

      if (disabled) {
        return cn(
          baseStyles,
          isDark
            ? "bg-transparent border border-white/60 text-white/60 opacity-40"
            : "bg-transparent border border-black/60 text-black/60 opacity-40"
        );
      }

      if (error) {
        return cn(
          baseStyles,
          isDark
            ? "bg-transparent border border-red-500 text-white/60 hover:border-red-400 focus:border-red-500"
            : "bg-transparent border border-red-500 text-black/60 hover:border-red-400 focus:border-red-500"
        );
      }

      return cn(
        baseStyles,
        isDark
          ? "bg-transparent border border-white/60 text-white/60 hover:border-white focus:border-white/60 placeholder:text-white/60"
          : "bg-transparent border border-black/60 text-black/60 hover:border-black focus:border-black/60 placeholder:text-black/60",
        {
          "pl-[40px]": !!Icon,
        }
      );
    };

    const handleSelectItem = (itemId: string | number, itemLabel: string) => {
      if (onItemSelect) {
        onItemSelect(itemId); // Pass the selected item's ID
      }
      setSearchTerm(itemLabel); // Set selected item's label in the input field
      setIsDropdownOpen(false); // Close dropdown after selection
    };

    return (
      <div className={cn("relative w-full", containerClassName)}>
        {label && (
          <label
            htmlFor={id}
            className={cn(
              "mb-[3px] block text-[15px] leading-[18px]",
              isDark ? "text-white/80" : "text-black/80"
            )}
          >
            {label}
          </label>
        )}
        <div className="relative">
          {!!Icon && (
            <Icon
              className={cn(
                "absolute left-3 top-1/2 -translate-y-1/2 size-4",
                isDark ? "text-white/60" : "text-black/60"
              )}
            />
          )}
          <input
            id={id}
            className={cn(getInputStyles(), className)}
            autoComplete="off"
            {...{
              placeholder,
              defaultValue,
              type,
              value: searchTerm, // Bind input value to the search term
              onChange: (e) => {
                setSearchTerm(e.target.value); // Update search term as user types
                setIsDropdownOpen(true); // Open dropdown when user types
              },
              onFocus: () => setIsDropdownOpen(true), // Open dropdown on input focus
              disabled,
              ref,
              ...rest,
            }}
          />
        </div>

        {isDropdownOpen && filteredItems.length > 0 && (
          <ul
            className={cn(
              "absolute z-10 mt-1 max-h-40 w-full overflow-auto rounded-md shadow-lg border",
              isDark
                ? "bg-back-neutral-primary border-white/60"
                : "bg-back-neutral-primary border-black/60"
            )}
            onMouseLeave={() => setIsDropdownOpen(false)}
            onMouseEnter={() => setIsDropdownOpen(true)}
          >
            {filteredItems.map((item) => (
              <button
                key={item.id}
                type="button"
                className={cn(
                  "w-full cursor-pointer p-2 text-left transition-colors duration-200",
                  isDark
                    ? "text-white/60 hover:bg-white/10 hover:text-white"
                    : "text-black/60 hover:bg-black/10 hover:text-black"
                )}
                onClick={() => handleSelectItem(item.id, item.label)}
              >
                {item.label}
              </button>
            ))}
          </ul>
        )}

        {!disableError && error && (
          <span className="mt-[3px] block h-[14px] text-[12px] text-red-500">
            {error}
          </span>
        )}
      </div>
    );
  }
);

AppInputDropdown.displayName = "AppInputDropdown";
