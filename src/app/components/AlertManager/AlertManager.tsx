import axios from "axios";
import React, { useState } from "react";
import { AppCode } from "../app-code";
import { AppPageTitle } from "../app-page-title";
import { AppButton } from "../app-button";
import { Toaster, toast } from "react-hot-toast";

interface AlertManagerProps {
  isRecipe: boolean;
  isRecurring: boolean;
  refetch: () => void;
  data: any; // can be either recipe or recurring data
}

interface EditingAlert {
  alertId: string;
  threshold: number;
  webhookUrl: string;
  editing: boolean;
  telegramHandle?: string;
}

export default function AlertManager({
  isRecipe,
  isRecurring,
  refetch,
  data,
}: AlertManagerProps) {
  const [alertThreshold, setAlertThreshold] = useState<number>(1);
  const [alertWebhookUrl, setAlertWebhookUrl] = useState<string>("");
  const [isButtonLoading, setIsButtonLoading] = useState(false);
  const [showAlertField, setShowAlertField] = useState<boolean>(false);
  const [isEditingAlert, setIsEditingAlert] = useState<EditingAlert>({
    alertId: "",
    threshold: 0,
    webhookUrl: "",
    editing: false,
  });
  const [telegramUsername, setTelegramUsername] = useState<string>("");
  const [showTGAlert, setShowTGAlert] = useState<boolean>(false);
  const [showWebhookAlert, setShowWebhookAlert] = useState<boolean>(false);

  const toggleAlertHandler = async (alertId: string) => {
    setIsButtonLoading(true);
    const createdAlert = await axios({
      method: "POST",
      url: `/api/alertToggle`,
      data: {
        alertId,
      },
    });
    if (createdAlert.status === 200) {
      toast.success("Alert status toggled");

      refetch();
    } else {
      toast.error("Couldn't change alert status");
    }
    setIsButtonLoading(false);
  };

  const deleteAlertHandler = async (alertId: string) => {
    setIsButtonLoading(true);
    const createdAlert = await axios({
      method: "POST",
      url: `/api/alertDelete`,
      data: {
        alertId,
      },
    });
    if (createdAlert.status === 200) {
      toast.success("Alert deleted");
      refetch();
    } else {
      toast.error("Couldn't delete alert");
    }
    setIsButtonLoading(false);
  };

  const recipeAlertCreationHandler = async (recipeId: string) => {
    setIsButtonLoading(true);
    if (!telegramUsername && alertWebhookUrl === "") {
      toast.error("Please fill either TG Username or Webhook URL");
      setIsButtonLoading(false);
      return;
    }
    let createdAlert;
    if (telegramUsername) {
      const chatId = await testTelegramUsername(telegramUsername);
      if (!chatId) {
        setIsButtonLoading(false);
        return;
      }

      createdAlert = await axios({
        method: "POST",
        url: `/api/alertRecipeCreate`,
        data: {
          webhookUrl: alertWebhookUrl,
          threshold: alertThreshold,
          recipeId,
          telegramUsername,
          chatId,
        },
      });
      if (createdAlert.status === 200) {
        setTelegramUsername("");
      }
      await axios({
        method: "POST",
        url: `/api/telegram/sendMessage`,
        data: {
          chatId,
          text: `You have successfully subscribed to alerts for recipe ${recipeId}
You set a threshold of ${alertThreshold} alert${alertThreshold > 1 ? "s" : ""}
          `,
        },
      });
    } else {
      createdAlert = await axios({
        method: "POST",
        url: `/api/alertRecipeCreate`,
        data: {
          webhookUrl: alertWebhookUrl,
          threshold: alertThreshold,
          recipeId,
        },
      });
    }
    if (createdAlert.status === 200) {
      setAlertThreshold(1);
      setAlertWebhookUrl("");
      setTelegramUsername("");
      refetch();
      toast.success("Alert created");
    } else {
      toast.error("Couldn't create alert");
    }
    setIsButtonLoading(false);
  };

  const recurringAlertCreationHandler = async (recurringJobId: string) => {
    setIsButtonLoading(true);
    let createdAlert;
    if (telegramUsername) {
      const chatId = await testTelegramUsername(telegramUsername);
      if (!chatId) {
        setIsButtonLoading(false);
        return;
      }

      createdAlert = await axios({
        method: "POST",
        url: `/api/alertRecurringCreate`,
        data: {
          webhookUrl: alertWebhookUrl,
          threshold: alertThreshold,
          recurringJobId,
          telegramUsername,
          chatId,
        },
      });
      if (createdAlert.status === 200) {
        setTelegramUsername("");
      }
      await axios({
        method: "POST",
        url: `/api/telegram/sendMessage`,
        data: {
          chatId,
          text: `You have successfully subscribed to alerts for ${
            isRecipe ? "recipe" : "recurring job"
          } ${recurringJobId}
You set a threshold of ${alertThreshold} alert${alertThreshold > 1 ? "s" : ""}
          `,
        },
      });
    } else {
      createdAlert = await axios({
        method: "POST",
        url: `/api/alertRecipeCreate`,
        data: {
          webhookUrl: alertWebhookUrl,
          threshold: alertThreshold,
          recurringJobId,
        },
      });
    }
    if (createdAlert.status === 200) {
      setAlertThreshold(1);
      setAlertWebhookUrl("");
      setTelegramUsername("");
      refetch();
      toast.success("Alert created");
    } else {
      toast.error("Couldn't create alert");
    }
    setIsButtonLoading(false);
  };
  const alertCreationHandler = isRecipe
    ? recipeAlertCreationHandler
    : recurringAlertCreationHandler;

  const updateAlertHandler = async () => {
    setIsButtonLoading(true);
    try {
      let response;
      if (isEditingAlert.telegramHandle) {
        const chatId = await testTelegramUsername(
          isEditingAlert.telegramHandle
        );
        if (!chatId) {
          setIsButtonLoading(false);
          return;
        }
        response = await axios({
          method: "POST",
          url: `/api/alertUpdate`,
          data: {
            alertId: isEditingAlert.alertId,
            threshold: isEditingAlert.threshold,
            webhookUrl: isEditingAlert.webhookUrl,
            telegramHandle: isEditingAlert.telegramHandle,
            chatId: chatId,
          },
        });
      } else {
        response = await axios({
          method: "POST",
          url: `/api/alertUpdate`,
          data: {
            alertId: isEditingAlert.alertId,
            threshold: isEditingAlert.threshold,
            webhookUrl: isEditingAlert.webhookUrl,
          },
        });
      }
      if (response.status === 200) {
        setIsEditingAlert({
          alertId: "",
          threshold: 0,
          webhookUrl: "",
          editing: false,
          telegramHandle: "",
        });
        toast.success("Alert updated");
        refetch();
      }
    } catch (error) {
      toast.error("Failed to update alert");
    }
    setIsButtonLoading(false);
  };

  const editAlertHandler = (alert: any) => {
    setIsEditingAlert({
      alertId: alert.id,
      threshold: alert.threshold,
      webhookUrl: alert.webhookUrl,
      editing: true,
      telegramHandle: alert.telegramHandle,
    });
    setShowAlertField(true);
  };

  const setTelegramUsernameHandler = (e: any) => {
    if (e.target.value.startsWith("@")) {
      const trimmed = e.target.value.replace("@", "");
      setTelegramUsername(trimmed);
    } else {
      setTelegramUsername(e.target.value);
    }
  };

  const testTelegramUsername = async (TGuserName: string) => {
    setIsButtonLoading(true);
    try {
      const telegramChatId = await axios({
        method: "POST",
        url: `/api/telegram/testChat`,
        data: {
          telegramUsername: TGuserName,
        },
      });
      if (telegramChatId.status === 200) {
        toast.success("Telegram username is correct and chat is created");
      }
      return telegramChatId.data.data;
    } catch (err) {
      toast.error("Telegram username is incorrect or chat is not created");
      return undefined;
    } finally {
      setIsButtonLoading(false);
    }
  };

  return (
    <div className="m-auto min-h-screen w-full grow overflow-y-auto bg-back-neutral-secondary dark:bg-back-neutral-primary">
      <Toaster position="top-center" reverseOrder={false} />
      <div className="mb-[45px] pl-[45px] pt-[45px]">
        <AppPageTitle>
          {isRecipe ? "Recipe" : "Recurring"} Alert Management
        </AppPageTitle>
      </div>
      {data ? (
        <div className="m-auto w-4/5">
          <AppCode code={JSON.stringify(data, null, 2)} language="json" />
          <div className="mt-6 w-full">
            <div className="mb-5 flex w-full flex-row justify-between">
              <AppButton onClick={() => setShowAlertField(!showAlertField)}>
                Manage alerts
              </AppButton>
            </div>

            {!isButtonLoading && showAlertField ? (
              <div className="flex w-full flex-row justify-between">
                <div className="flex w-2/5 flex-col gap-2">
                  <h3 className="text-[23px] leading-[25px] text-fore-neutral-primary">
                    {isEditingAlert.editing ? "Edit alert" : "Create alert"}
                  </h3>
                  <label htmlFor="telegramUsername" className="text-white">
                    Threshold:
                  </label>
                  <input
                    className="border-border h-[41px] w-full min-w-[230px] rounded-[4px] border border-stroke-neutral-decorative bg-back-neutral-primary pl-[10px] italic text-fore-neutral-secondary outline-none"
                    type="number"
                    placeholder="threshold"
                    value={
                      isEditingAlert.editing
                        ? isEditingAlert.threshold
                        : alertThreshold
                    }
                    onChange={(e) =>
                      isEditingAlert.editing
                        ? setIsEditingAlert({
                            ...isEditingAlert,
                            threshold: Number(e.target.value),
                          })
                        : setAlertThreshold(Number(e.target.value))
                    }
                  />
                  {!isEditingAlert.editing && (
                    <AppButton
                      onClick={() => setShowWebhookAlert(!showWebhookAlert)}
                    >
                      {!showWebhookAlert ? "Add" : "Remove"} webhook
                    </AppButton>
                  )}
                  {showWebhookAlert || isEditingAlert.editing ? (
                    <input
                      className="border-border h-[41px] w-full min-w-[230px] rounded-[4px] border border-stroke-neutral-decorative bg-back-neutral-primary pl-[10px] italic text-fore-neutral-secondary outline-none"
                      type="text"
                      placeholder="webhookUrl"
                      value={
                        isEditingAlert.editing
                          ? isEditingAlert.webhookUrl
                          : alertWebhookUrl
                      }
                      onChange={(e) =>
                        isEditingAlert.editing
                          ? setIsEditingAlert({
                              ...isEditingAlert,
                              webhookUrl: e.target.value,
                            })
                          : setAlertWebhookUrl(e.target.value)
                      }
                    />
                  ) : (
                    ""
                  )}
                  {!isEditingAlert.editing && (
                    <AppButton onClick={() => setShowTGAlert(!showTGAlert)}>
                      {!showTGAlert ? "Add" : "Remove"} TG Alert
                    </AppButton>
                  )}
                  {showTGAlert || isEditingAlert.editing ? (
                    <>
                      <input
                        className="border-border h-[41px] w-full min-w-[230px] rounded-[4px] border border-stroke-neutral-decorative bg-back-neutral-primary pl-[10px] italic text-fore-neutral-secondary outline-none"
                        type="text"
                        placeholder="telegram username (opt)"
                        value={
                          isEditingAlert.editing
                            ? isEditingAlert.telegramHandle
                            : telegramUsername
                        }
                        onChange={(e) =>
                          isEditingAlert.editing
                            ? setIsEditingAlert({
                                ...isEditingAlert,
                                telegramHandle: e.target.value,
                              })
                            : setTelegramUsernameHandler(e)
                        }
                      />
                      <label htmlFor="telegramUsername" className="text-white">
                        * Case sensitive, No need the @ symbol
                      </label>
                      {telegramUsername && (
                        <a
                          rel="noreferrer"
                          target="_blank"
                          href={`https://t.me/GetRecon_bot`}
                          className="text-white underline"
                        >
                          Start a chat with the bot
                        </a>
                      )}
                      <AppButton
                        onClick={() => testTelegramUsername(telegramUsername)}
                      >
                        Test Telegram Username
                      </AppButton>
                    </>
                  ) : (
                    ""
                  )}

                  {isEditingAlert.editing ? (
                    <div className="flex gap-2">
                      <AppButton onClick={updateAlertHandler}>
                        Update Alert
                      </AppButton>
                      <AppButton
                        onClick={() =>
                          setIsEditingAlert({
                            alertId: "",
                            threshold: 0,
                            webhookUrl: "",
                            editing: false,
                          })
                        }
                      >
                        Cancel
                      </AppButton>
                    </div>
                  ) : (
                    <AppButton onClick={() => alertCreationHandler(data.id)}>
                      Create Alert
                    </AppButton>
                  )}
                </div>
                <div className="flex w-2/5  flex-col">
                  <h3 className="text-[23px] leading-[25px] text-fore-neutral-primary">
                    Current alerts
                  </h3>
                  {data.alerts.map((alert) => (
                    <div key={alert.id} className="mb-5 w-full">
                      <p className="text-white">
                        - Threshold: {alert.threshold}
                      </p>
                      <p className="text-white">
                        - Webhook: {alert.webhookUrl}
                      </p>
                      {alert.telegramHandle ? (
                        <p className="text-white">
                          - TG username: {alert.telegramHandle}
                        </p>
                      ) : (
                        ""
                      )}
                      <p className="mb-3 text-white">
                        - Active: {alert.active ? "✅" : "❌"}
                      </p>
                      <AppButton
                        className="mr-2"
                        onClick={() => deleteAlertHandler(alert.id)}
                      >
                        Delete Alert
                      </AppButton>
                      <AppButton
                        className="mr-2"
                        onClick={() => toggleAlertHandler(alert.id)}
                      >
                        {alert.active ? "Disable Alert" : "Enable Alert"}
                      </AppButton>
                      <AppButton
                        className="mr-2"
                        onClick={() => editAlertHandler(alert)}
                      >
                        Edit alert
                      </AppButton>
                    </div>
                  ))}
                </div>
              </div>
            ) : null}
            {isButtonLoading && (
              <p className="text-white">Performing action ... </p>
            )}
          </div>
        </div>
      ) : (
        ""
      )}
    </div>
  );
}
