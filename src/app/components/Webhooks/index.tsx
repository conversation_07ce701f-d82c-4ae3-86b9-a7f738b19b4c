"use client";

import axios from "axios";
import Link from "next/link";
import { useState } from "react";

import type { Job } from "@/app/services/jobs.hooks";
import { useGetJobs } from "@/app/services/jobs.hooks";
import type { Webhook } from "@/app/services/webhooks.hook";
import { useGetWebhooks } from "@/app/services/webhooks.hook";
import { formatDateString } from "@/utils/format";

import { AppButton } from "../app-button";

const ONE_HOUR_MS = 1000 * 60 * 60; // 1 hour

export const Webhooks = () => {
  const { data: webhooks } = useGetWebhooks();
  const { refetch: refetchJobs } = useGetJobs();

  const [loading, setLoading] = useState(false);
  const [queuedJobId, setQueuedJobId] = useState("");

  const filtered = webhooks?.filter(
    (event) =>
      new Date(event.updatedAt).getTime() > new Date().getTime() - ONE_HOUR_MS
  );

  const startMedusaJob = async (hook: Webhook) => {
    if (loading) {
      return;
    }

    setLoading(false);

    try {
      const foundData = await axios({
        method: "POST",
        url: `/api/jobs/medusa`,
        data: {
          orgName: hook.orgName,
          repoName: hook.repoName,
          ref: hook.ref, // TODO: Defaults
        },
      });

      const jobInfo = foundData?.data as Job;
      setQueuedJobId(jobInfo?.id);

      refetchJobs();
    } catch (e) {
      console.log("e", e);
      alert(`Something went wrong: ${e.response.data.message}`);
    }
    setLoading(false);
  };

  return (
    <div className="text-fore-neutral-primary">
      {queuedJobId == "" && (
        <>
          {filtered?.length > 0 && <h2>Recent Commits</h2>}
          {filtered?.map((event) => (
            <h3 key={event.id}>
              {event.orgName} - {event.repoName} -{" "}
              {formatDateString(event.updatedAt)}{" "}
              <AppButton onClick={() => startMedusaJob(event)}>
                Create Job
              </AppButton>
            </h3>
          ))}
        </>
      )}
      {queuedJobId != "" && (
        <h3>
          Queued with id:{" "}
          <Link href={`/dashboard/jobs/${queuedJobId}`}>{queuedJobId}</Link>
        </h3>
      )}
    </div>
  );
};
