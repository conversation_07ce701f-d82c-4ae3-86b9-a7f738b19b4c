"use client";

import axios from "axios";
import { useState } from "react";
import { FaCheck, FaLink } from "react-icons/fa";

import { AppButton } from "@/app/components/app-button";
import { AppSpinner } from "@/app/components/app-spinner";

export const ShareJobButton = ({ jobId, reloadShares, shareInfo }) => {
  const [loading, setLoading] = useState(false);

  async function shareTheJob() {
    if (!jobId) {
      return;
    }

    setLoading(true);

    try {
      await axios({
        method: "POST",
        url: "/api/shares",
        data: {
          jobId,
        },
      });
    } catch (e) {
      console.log(e);
      alert(`Something went wrong: ${e.response.data.message}`);
    }

    reloadShares();
    setLoading(false);
  }

  // TODO: if shareInfo
  // Then turn it into clipboard copy tool

  const [copied, setCopied] = useState(false);

  function handleCopy() {
    navigator.clipboard.writeText(
      `${window.location.origin}/shares/${shareInfo.id}`
    );
    setCopied(true);
  }

  if (shareInfo) {
    return (
      <AppButton
        onClick={handleCopy}
        variant="primary"
        rightIcon={copied ? <FaCheck /> : <FaLink />}
      >
        Copy Share URL
      </AppButton>
    );
  }

  return (
    <AppButton disabled={loading} onClick={shareTheJob}>
      {loading ? (
        <AppSpinner />
      ) : (
        <>
          <span>Share Job Results</span>
          <FaLink />
        </>
      )}
    </AppButton>
  );
};
