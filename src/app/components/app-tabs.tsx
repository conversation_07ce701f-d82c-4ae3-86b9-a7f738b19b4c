import type { ReactElement, ReactNode } from "react";
import { useState } from "react";

import { cn } from "../helpers/cn";

interface AppTabPaneProps {
  tab: string;
  label: string;
  children: ReactNode;
}

const AppTabPane = ({ children }: AppTabPaneProps) => {
  return <div>{children}</div>;
};

interface AppTabsProps {
  defaultActiveKey?: string;
  children: ReactElement<AppTabPaneProps>[];
  className?: string;
}

const AppTabs = ({ children, defaultActiveKey, className }: AppTabsProps) => {
  const [activeKey, setActiveKey] = useState(
    defaultActiveKey || children[0].props.tab
  );

  return (
    <div className={className}>
      <div className="flex gap-[60px] ">
        {children.map((child) => {
          const isActive = activeKey === child.props.tab;
          return (
            <button
              key={child.props.tab}
              className={cn(
                "border-b-[4px] border-solid bg-transparent text-fore-neutral-primary text-[22px] leading-[26px] pb-[4px]]",
                {
                  "border-accent-primary": isActive,
                  "border-transparent": !isActive,
                }
              )}
              onClick={() => setActiveKey(child.props.tab)}
              onKeyDown={(event) => {
                if (event.key === "Enter" || event.key === " ") {
                  setActiveKey(child.props.tab);
                }
              }}
            >
              {child.props.label}
            </button>
          );
        })}
      </div>
      <div className="pt-[32px]">
        {children.map((child) =>
          activeKey === child.props.tab ? (
            <AppTabPane key={child.props.tab} {...child.props} />
          ) : null
        )}
      </div>
    </div>
  );
};

export { AppTabPane, AppTabs };
