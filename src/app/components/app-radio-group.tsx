import { useMemo } from "react";

import { cn } from "../helpers/cn";
import { THEME_OPTIONS } from "../services/ThemeProvider";
import { usePersistedTheme } from "../services/useThemePersistence";

type AppRadioGroupProps = {
  options: { label: string; value: string }[];
  name: string;
  onChange: (value: string) => void;
  value: string;
};

export const AppRadioGroup = ({
  options,
  name,
  onChange,
  value,
}: AppRadioGroupProps) => {
  const { theme } = usePersistedTheme();
  const handleChange = (newValue: string) => {
    onChange(newValue);
  };

  // NOTE! Race condition fixed with usePersistedTheme
  // cn uses memoized theme, so you need to memo
  // Because theme is async loaded in FE
  const classForTailwind = useMemo(() => {
    if (theme === THEME_OPTIONS.light) {
      return "bg-black";
    }

    return "bg-white";
  }, [theme]);

  return (
    <div className="flex items-center justify-between border border-x-0 border-stroke-neutral-decorative p-4 py-[20px]">
      {options.map((option, index) => (
        <label key={index} className="flex cursor-pointer items-center">
          <input
            type="radio"
            name={name}
            value={option.value ?? ""}
            checked={value === option.value}
            onChange={() => handleChange(option.value)}
            className="absolute size-0 opacity-0"
          />
          <div className="mr-2 flex size-[14px] items-center justify-center rounded-full border border-fore-neutral-primary">
            <span
              className={cn("w-[8px] h-[8px] rounded-full", {
                [classForTailwind]: value === option.value,
                "bg-transparent": value !== option.value,
              })}
            />
          </div>

          <span className="text-fore-neutral-primary">{option.label}</span>
        </label>
      ))}
    </div>
  );
};
