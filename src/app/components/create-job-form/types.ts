import type { ENV_TYPE } from "@/app/app.constants";

export interface PreparedDynamicReplacementContract {
  target: string;
  replacement: string;
  endOfTargetMarker: string;
  targetContract: string;
}

export type DynamicReplacementFieldsGroup = {
  variableName: string;
  interface: string;
  value: string;
};

export type GitHubLinkFormValues = {
  // UX
  githubURL: string;

  // Basic
  orgName: string;
  repoName: string;
  ref: string;
  directory: string;
  customOut: string;

  // Recipe specific
  displayName?: string;

  // Job specific
  label?: string;

  // Preprocess / Compatibility
  preprocess: string;
  targetCorpus: string;

  // Medusa
  medusaConfig: string;
  timeout: string;

  // Echidna
  pathToTester: string;
  echidnaConfig: string;
  forkBlock: string;
  forkMode: string;
  contract: string;
  corpusDir: string;
  rpcUrl: string;
  testLimit: string;
  testMode: string;
  forkReplacement: boolean | string;

  // Foundry
  runs: string;
  seed: string;
  verbosity: string;
  testCommand: string;
  testTarget: string;

  // Halmos
  halmosPrefix: string;
  halmosArray: string;
  halmosLoops: string;

  // Kontrol
  kontrolTest: string;

  // Dynamic replacement
  prepareContracts: PreparedDynamicReplacementContract[];
  fields: DynamicReplacementFieldsGroup[];
};

export type FormMode = "job" | "recipe";

export interface BaseFormProps {
  title?: string;
  submitLabel?: string;
  hideEnv?: boolean;
  hidePresets?: boolean;
}

export interface JobFormProps extends BaseFormProps {
  onSubmit(data: GitHubLinkFormValues): void;
  env?: ENV_TYPE;
  setEnv?(env: ENV_TYPE): void;
  jobId?: number | null;
  buildHandler?: boolean;
  setRecipeId?: (id: string) => void;
}

export interface RecipeFormProps extends BaseFormProps {
  editId?: string;
}
