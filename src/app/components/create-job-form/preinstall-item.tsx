import React from "react";
import { useFormContext } from "react-hook-form";

import { AppSelect } from "../app-select";
import { PREPROCESS_OPTIONS, FORM_STYLES } from "./constants";

export const PreInstallItem = () => {
  const { register } = useFormContext();

  return (
    <AppSelect
      className={FORM_STYLES.input}
      options={PREPROCESS_OPTIONS}
      label="Custom pre-install process"
      {...register("preprocess")}
    />
  );
};
