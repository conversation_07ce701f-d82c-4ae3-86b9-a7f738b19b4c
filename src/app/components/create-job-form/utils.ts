import type { UseFormSetError, UseFormSetValue } from "react-hook-form";
import type { GitHubLinkFormValues } from "./types";

/**
 * Parses a GitHub URL and extracts organization, repository, and branch information
 */
export const parseGitHubURL = (
  inputValue: string,
  setValue: UseFormSetValue<GitHubLinkFormValues>,
  setError: UseFormSetError<GitHubLinkFormValues>
): void => {
  const success = /^(https?:\/\/)?(www\.)?github\.com\/.+\/.+(\.git)?$/.test(
    inputValue
  );

  if (!success) {
    setError("githubURL", {
      message: "Invalid GitHub URL",
    });
    return;
  } else {
    setError("githubURL", {});
  }

  const ghLink = inputValue.endsWith("/")
    ? inputValue.slice(0, -1)
    : inputValue;
  const uriParts = ghLink
    .replace("https://", "")
    .replace("http://", "")
    .split("/");

  if (uriParts.length >= 3) {
    const orgName = uriParts[1];
    const repoName = uriParts[2];
    let ref = "main"; // Default branch

    // Check if the URL specifies a branch
    if (uriParts.length > 5 && uriParts[3] === "tree") {
      // The branch name can include slashes, so we join the remaining parts
      ref = uriParts.slice(4).join("/");
    } else if (uriParts.length === 5 && uriParts[3] === "tree") {
      // Handle the case where there's no slash in the branch name
      ref = uriParts[4];
    }

    // Set the values to the form
    setValue("orgName", orgName);
    setValue("repoName", repoName);
    setValue("ref", ref);
  }
};

/**
 * Applies recipe defaults to form values
 */
export const applyRecipeDefaults = (
  defaults: any,
  setValue: UseFormSetValue<GitHubLinkFormValues>,
  setEnv?: (env: any) => void,
  setRecipeId?: (id: string) => void
): void => {
  if (setRecipeId) {
    setRecipeId(defaults.id);
  }

  // Set fuzzer type
  if (defaults?.fuzzer && setEnv) {
    setEnv(defaults.fuzzer);
  }

  // Update all keys except Fuzzer and FuzzerArgs
  const keys = Object.keys(defaults);
  const filtered = keys.filter(
    (key) => key !== "fuzzerArgs" && key !== "fuzzer"
  );

  for (const key of filtered) {
    setValue(key as keyof GitHubLinkFormValues, defaults[key]);
  }

  // Apply fuzzer-specific arguments
  if (defaults?.fuzzerArgs) {
    const fuzzerArgs = defaults.fuzzerArgs;

    // Common fields
    setValue("contract", fuzzerArgs.contract);
    setValue("forkBlock", fuzzerArgs.forkBlock);
    setValue("forkMode", fuzzerArgs.forkMode);
    setValue("rpcUrl", fuzzerArgs.rpcUrl);
    setValue("verbosity", fuzzerArgs.verbosity);
    setValue("label", fuzzerArgs.label);

    // Echidna specific
    setValue("pathToTester", fuzzerArgs.pathToTester);
    setValue("testLimit", fuzzerArgs.testLimit);
    setValue("echidnaConfig", fuzzerArgs.config);
    setValue("corpusDir", fuzzerArgs.corpusDir);
    setValue("forkReplacement", fuzzerArgs.forkReplacement);
    setValue("targetCorpus", fuzzerArgs.targetCorpus);
    setValue("testMode", fuzzerArgs.testMode);

    // Medusa specific
    setValue("timeout", fuzzerArgs.timeout);
    setValue("medusaConfig", fuzzerArgs.config);

    // Foundry specific
    setValue("runs", fuzzerArgs.runs);
    setValue("testCommand", fuzzerArgs.testCommand);
    setValue("testTarget", fuzzerArgs.testTarget);
    setValue("seed", fuzzerArgs.seed);

    // Halmos specific
    setValue("halmosPrefix", fuzzerArgs.halmosPrefix);
    setValue("halmosArray", fuzzerArgs.halmosArray);
    setValue("halmosLoops", fuzzerArgs.halmosLoops);

    // Kontrol specific
    setValue("kontrolTest", fuzzerArgs.kontrolTest);
  }
};

/**
 * Validates required fields based on the selected fuzzer type
 */
export const validateFormData = (
  data: GitHubLinkFormValues,
  env: string
): string[] => {
  const errors: string[] = [];

  // Common required fields
  if (!data.orgName) errors.push("Organization name is required");
  if (!data.repoName) errors.push("Repository name is required");
  if (!data.ref) errors.push("Branch is required");

  // Fuzzer-specific validation
  switch (env) {
    case "ECHIDNA":
      if (!data.pathToTester)
        errors.push("Path to tester is required for Echidna");
      if (!data.contract) errors.push("Contract name is required for Echidna");
      break;
    case "FOUNDRY":
      if (!data.contract) errors.push("Contract name is required for Foundry");
      break;
    case "HALMOS":
      if (!data.contract) errors.push("Contract name is required for Halmos");
      break;
    case "KONTROL":
      if (!data.kontrolTest) errors.push("Target test is required for Kontrol");
      break;
  }

  return errors;
};

/**
 * Creates validation rules for react-hook-form
 */
export const getValidationRules = (env: string) => {
  const baseRules = {
    githubURL: {
      required: "GitHub URL is required",
      pattern: {
        value: /^(https?:\/\/)?(www\.)?github\.com\/.+\/.+(\.git)?$/,
        message: "Invalid GitHub URL format",
      },
    },
    orgName: { required: "Organization name is required" },
    repoName: { required: "Repository name is required" },
    ref: { required: "Branch is required" },
  };

  const fuzzerSpecificRules: Record<string, any> = {
    ECHIDNA: {
      pathToTester: { required: "Path to tester is required for Echidna" },
      contract: { required: "Contract name is required for Echidna" },
    },
    FOUNDRY: {
      contract: { required: "Contract name is required for Foundry" },
    },
    HALMOS: {
      contract: { required: "Contract name is required for Halmos" },
    },
    KONTROL: {
      kontrolTest: { required: "Target test is required for Kontrol" },
    },
  };

  return {
    ...baseRules,
    ...(fuzzerSpecificRules[env] || {}),
  };
};
