import { useFormContext } from "react-hook-form";

import { AppInput } from "../app-input";
import { PreInstallItem } from "./preinstall-item";
import { FORM_STYLES } from "./constants";

export const SubFormMedusa = () => {
  const { register } = useFormContext();

  return (
    <div className={FORM_STYLES.formContainer}>
      <div className={FORM_STYLES.inputGroup}>
        <div className={FORM_STYLES.fieldContainer}>
          <AppInput
            className={FORM_STYLES.input}
            label="Medusa config filename"
            {...register("medusaConfig")}
            type="text"
            placeholder="medusa.json"
          />
        </div>
        <div className={FORM_STYLES.fieldContainer}>
          <AppInput
            className={FORM_STYLES.input}
            label="Test time limit (seconds)"
            {...register("timeout")}
            type="text"
            placeholder="3600"
          />
        </div>
      </div>
    </div>
  );
};

export const SubFormMedusaAdvanced = () => {
  const { register } = useFormContext();

  return (
    <div className={FORM_STYLES.formContainer}>
      <div className={FORM_STYLES.inputGroup}>
        <div className={FORM_STYLES.fieldContainer}>
          <AppInput
            className={FORM_STYLES.input}
            label="Corpus re-use job ID"
            {...register("targetCorpus")}
            type="text"
            placeholder="Optional: Job ID to reuse corpus from"
          />
        </div>
        <div className={FORM_STYLES.fieldContainer}>
          <PreInstallItem />
        </div>
      </div>
    </div>
  );
};
