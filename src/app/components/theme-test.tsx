"use client";

import { usePersistedTheme } from "../services/useThemePersistence";
import { THEME_OPTIONS } from "../services/ThemeProvider";

export const ThemeTest = () => {
  const { theme, setTheme, isDark, mounted } = usePersistedTheme();

  return (
    <div className="p-4 border rounded-lg">
      <h3 className="text-lg font-bold mb-2">Theme Test Component</h3>
      <p>Current theme: {theme}</p>
      <p>Is dark: {isDark ? "Yes" : "No"}</p>
      <p>Is mounted: {mounted ? "Yes" : "No"}</p>
      
      <div className="mt-4 space-x-2">
        <button
          onClick={() => setTheme(THEME_OPTIONS.light)}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Set Light
        </button>
        <button
          onClick={() => setTheme(THEME_OPTIONS.dark)}
          className="px-4 py-2 bg-gray-800 text-white rounded hover:bg-gray-900"
        >
          Set Dark
        </button>
      </div>
      
      <div className="mt-4 p-2 bg-back-neutral-primary text-fore-neutral-primary rounded">
        This div should change colors based on theme
      </div>
    </div>
  );
};
