"use client";

import { cn } from "@/lib/utils";
import { useState } from "react";
import { FiPlay } from "react-icons/fi";
import { Body3, Body4, H6 } from "../app-typography";

export const DASHBOARD_VIDEOS = [
  {
    title: "Recon Euler EVC Demo",
    url: "https://www.youtube.com/watch?v=z0sktBuJfEI",
    duration: "1min",
    thumbnail: "https://img.youtube.com/vi/z0sktBuJfEI/maxresdefault.jpg",
  },
  {
    title: "5 minutes tutorial",
    url: "https://www.youtube.com/watch?v=RwfiPrxbdBg",
    duration: "9min",
    thumbnail: "https://img.youtube.com/vi/RwfiPrxbdBg/maxresdefault.jpg",
  },
  {
    title: "15 minutes product tour",
    url: "https://www.youtube.com/watch?v=TvCm6MCHKs0",
    duration: "18min",
    thumbnail: "https://img.youtube.com/vi/TvCm6MCHKs0/maxresdefault.jpg",
  },
  {
    title: "1 Hour workshop on writing better invariants",
    url: "https://www.youtube.com/watch?v=fXG2JwvoFZ0",
    duration: "46min",
    thumbnail: "https://img.youtube.com/vi/fXG2JwvoFZ0/maxresdefault.jpg",
  },
];

export const PRO_VIDEOS = [
  {
    title: "Run a job in the cloud",
    url: "https://www.youtube.com/watch?v=s4ci9zgIHiI",
    duration: "1min",
    thumbnail: "https://img.youtube.com/vi/s4ci9zgIHiI/maxresdefault.jpg",
  },
  {
    title: "Create and re-use recipes",
    url: "https://www.youtube.com/watch?v=bXT8Ye2EaGs",
    duration: "2min",
    thumbnail: "https://img.youtube.com/vi/bXT8Ye2EaGs/maxresdefault.jpg",
  },
  {
    title: "Run jobs on PR and commit",
    url: "https://www.youtube.com/watch?v=Fnz4P5kxAD0",
    duration: "1min",
    thumbnail: "https://img.youtube.com/vi/Fnz4P5kxAD0/maxresdefault.jpg",
  },
];

type AppVideo = {
  title: string;
  url: string;
  duration: string;
  thumbnail: string;
};

interface VideoCardProps {
  video: AppVideo;
  isActive?: boolean;
  onClick: () => void;
}

const VideoCard = ({ video, isActive = false, onClick }: VideoCardProps) => {
  return (
    <div
      className={cn(
        "flex cursor-pointer gap-3 rounded-lg border p-3 transition-all duration-200 hover:bg-back-neutral-tertiary",
        isActive
          ? "bg-back-accent-secondary border-accent-primary"
          : "border-stroke-neutral-decorative bg-back-neutral-secondary"
      )}
      onClick={onClick}
    >
      <div className="relative h-16 w-24 shrink-0 overflow-hidden rounded-md bg-back-neutral-tertiary">
        <img
          src={video.thumbnail}
          alt={video.title}
          className="size-full object-cover"
          onError={(e) => {
            e.currentTarget.style.display = "none";
            e.currentTarget.nextElementSibling?.classList.remove("hidden");
          }}
        />
        <div className="absolute inset-0 hidden bg-gradient-to-br from-accent-primary/20 to-accent-secondary/20">
          <div className="flex size-full items-center justify-center">
            <div className="flex size-8 items-center justify-center rounded-full bg-white/90">
              <FiPlay className="ml-0.5 size-4 text-accent-primary" />
            </div>
          </div>
        </div>

        <div className="absolute inset-0 flex items-center justify-center opacity-0 transition-opacity duration-200 hover:opacity-100">
          <div className="flex size-8 items-center justify-center rounded-full bg-black/50 backdrop-blur-sm">
            <FiPlay className="ml-0.5 size-4 text-white" />
          </div>
        </div>

        <div className="absolute bottom-1 right-1 rounded bg-black/70 px-1.5 py-0.5">
          <Body4 className="text-xs text-white">{video.duration}</Body4>
        </div>
      </div>

      <div className="flex flex-1 flex-col justify-center gap-1">
        <Body3
          className={cn(
            "line-clamp-2 font-medium",
            isActive ? "text-accent-primary" : "text-fore-neutral-primary"
          )}
        >
          {video.title}
        </Body3>
      </div>
    </div>
  );
};

export const AppVideoCarousel = ({ videos }: { videos: AppVideo[] }) => {
  const [selectedVideo, setSelectedVideo] = useState(videos[0]);

  const handleVideoSelect = (video: AppVideo) => {
    setSelectedVideo(video);
  };

  const nextVideos = videos.filter((video) => video.url !== selectedVideo.url);

  return (
    <div className="flex flex-col gap-6">
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-[2fr_1fr]">
        <div>
          <div className="relative aspect-video w-full overflow-hidden rounded-lg bg-back-neutral-tertiary">
            <iframe
              src={
                selectedVideo?.url.replace("watch?v=", "embed/") +
                "?rel=0&modestbranding=1"
              }
              title={selectedVideo?.title}
              className="absolute inset-0 size-full"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
              allowFullScreen
            />
          </div>

          <div className="mt-4 space-y-2">
            <H6 color="primary">{selectedVideo.title}</H6>
            <div className="flex items-center gap-2">
              <Body4 color="tertiary">{selectedVideo.duration}</Body4>
            </div>
          </div>
        </div>

        <div className="pb-6">
          <div className="space-y-3">
            <Body3 color="secondary" className="font-medium">
              Next Videos ({nextVideos.length})
            </Body3>

            <div className="max-h-[500px] space-y-2 overflow-y-auto">
              {nextVideos.map((video) => (
                <VideoCard
                  key={video.url}
                  video={video}
                  isActive={false}
                  onClick={() => handleVideoSelect(video)}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
