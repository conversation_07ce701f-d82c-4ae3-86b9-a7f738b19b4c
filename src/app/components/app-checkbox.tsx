import { forwardRef, useId } from "react";
import type { IconType } from "react-icons/lib";

import { cn } from "../helpers/cn";

type AppCheckboxProps = {
  className?: string;
  defaultChecked?: boolean;
  disabled?: boolean;
  onChange?: (e: any) => void;
  checked?: boolean;
  label?: string;
  containerClassName?: string;
  error?: string;
  disableError?: boolean;
  icon?: IconType;
  tooltip?: string; // Tooltip text
};

export const AppCheckbox = forwardRef(
  (
    {
      onChange,
      defaultChecked,
      disabled,
      checked,
      className = "",
      label,
      containerClassName = "",
      error,
      icon: Icon,
      disableError = false,
      tooltip, // Tooltip text
      ...rest
    }: AppCheckboxProps,
    ref: any
  ) => {
    const id = useId();

    return (
      <div
        className={cn(
          "relative flex items-center gap-3 mb-4",
          containerClassName
        )}
      >
        <div className="relative group">
          <input
            id={id}
            type="checkbox"
            className={cn(
              "h-5 w-5 border border-gray-300 rounded-sm bg-white text-accent-primary outline-none transition-colors duration-200 ease-in-out focus:ring-2 focus:ring-accent-primary focus:ring-offset-2",
              {
                "opacity-50 cursor-not-allowed": disabled,
              },
              className
            )}
            {...{
              defaultChecked,
              checked,
              onChange,
              disabled,
              ref,
              ...rest,
            }}
          />
          {tooltip && (
            <div className="absolute bottom-[130%] left-1/2 transform -translate-x-1/2 mb-2 hidden w-max max-w-xs px-3 py-1 text-sm text-white bg-black rounded-md shadow-lg group-hover:block">
              {tooltip}
            </div>
          )}
        </div>
        {label && (
          <label
            htmlFor={id}
            className={cn(
              "text-[15px] leading-[18px] text-fore-neutral-secondary cursor-pointer",
              {
                "text-gray-400": disabled,
              }
            )}
          >
            {label}
          </label>
        )}
        {!!Icon && (
          <Icon className="absolute left-2 top-1/2 -translate-y-1/2 text-fore-neutral-secondary" />
        )}
        {!disableError && error && (
          <span className="mt-[3px] block h-[14px] text-[12px] text-red-600">
            {error}
          </span>
        )}
      </div>
    );
  }
);

AppCheckbox.displayName = "AppCheckbox";
