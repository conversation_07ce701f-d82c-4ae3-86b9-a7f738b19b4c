import { sortProperties } from "@/app/(app)/tools/mdReportHelper";
import type { FuzzingResults } from "@recon-fuzz/log-parser";
import React from "react";
import { H2, H3, Body2, Body3 } from "@/app/components/app-typography";

interface JobReportProps {
  fuzzer: string;
  jobStats: FuzzingResults;
  showBrokenProp: boolean;
}

export default function JobReport({
  fuzzer,
  jobStats,
  showBrokenProp,
}: JobReportProps) {
  return (
    <div className="w-full overflow-x-hidden">
      <div className="mb-6">
        <H2 className="mb-4" color="primary">
          Fuzzing overview
        </H2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Body2 color="primary">
              <span className="font-semibold">Fuzzer:</span> {fuzzer}
            </Body2>
            <Body2 color="primary">
              <span className="font-semibold">Duration:</span>{" "}
              {jobStats.duration}
            </Body2>
          </div>
          <div className="space-y-2">
            <Body2 color="primary">
              <span className="font-semibold">Failed tests:</span>{" "}
              {jobStats.failed}
            </Body2>
            <Body2 color="primary">
              <span className="font-semibold">Passed tests:</span>{" "}
              {jobStats.passed}
            </Body2>
          </div>
        </div>
      </div>
      {showBrokenProp && jobStats.brokenProperties?.length > 0 && (
        <div className="mb-6">
          <H3 className="mb-4" color="primary">
            Broken properties
          </H3>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse">
              <thead>
                <tr className="border-b border-stroke-neutral-decorative">
                  <th className="text-left py-3 px-4">
                    <Body2 color="secondary" className="font-semibold">
                      #
                    </Body2>
                  </th>
                  <th className="text-left py-3 px-4">
                    <Body2 color="secondary" className="font-semibold">
                      Property
                    </Body2>
                  </th>
                </tr>
              </thead>
              <tbody>
                {jobStats.brokenProperties.map((el, index) => (
                  <tr
                    key={`${el.brokenProperty}-${index}`}
                    className="border-b border-stroke-neutral-decorative hover:bg-back-neutral-tertiary"
                  >
                    <td className="py-3 px-4">
                      <Body3 color="primary">{index + 1}</Body3>
                    </td>
                    <td className="py-3 px-4">
                      <Body3 color="primary">{el.brokenProperty}</Body3>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
      <div className="mb-6">
        <H3 className="mb-4" color="primary">
          Properties fuzzed
        </H3>
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr className="border-b border-stroke-neutral-decorative">
                <th className="text-left py-3 px-4">
                  <Body2 color="secondary" className="font-semibold">
                    Property
                  </Body2>
                </th>
                <th className="text-left py-3 px-4">
                  <Body2 color="secondary" className="font-semibold">
                    Status
                  </Body2>
                </th>
              </tr>
            </thead>
            <tbody>
              {sortProperties(jobStats.results).map(
                ({ property, status, index }) => (
                  <tr
                    key={`${property}-${index}`}
                    className="border-b border-stroke-neutral-decorative hover:bg-back-neutral-tertiary"
                  >
                    <td className="py-3 px-4">
                      <Body3 color="primary">{property}</Body3>
                    </td>
                    <td className="py-3 px-4">
                      <Body3
                        color={
                          status === "passed"
                            ? "accent"
                            : status === "failed"
                              ? "primary"
                              : "secondary"
                        }
                        className={status === "failed" ? "text-red-500" : ""}
                      >
                        {status}
                      </Body3>
                    </td>
                  </tr>
                )
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
