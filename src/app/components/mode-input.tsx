import { forwardRef, useId } from "react";

import { cn } from "../helpers/cn";

type ModeInputProps = {
  className?: string;
  defaultValue?: string;
  disabled?: boolean;
  onChange?: (e: React.ChangeEvent<HTMLSelectElement>) => void;
  value?: string;
  placeholder?: string;
  label?: string;
  containerClassName?: string;
  error?: string;
  disableError?: boolean;
  options?: { label: string; value: string }[];
};

export const ModeInput = forwardRef<HTMLSelectElement, ModeInputProps>(
  (
    {
      onChange,
      disabled,
      value,
      placeholder = "",
      className = "",
      label,
      containerClassName = "",
      error,
      defaultValue = "config",
      disableError = false,
      options = [
        { label: "Use config", value: "config" },
        { label: "exploration", value: "exploration" },
        { label: "optimize", value: "optimize" },
        { label: "assertion", value: "assertion" },
        { label: "property", value: "property" },
      ],
      ...rest
    },
    ref
  ) => {
    const id = useId();

    return (
      <div className={cn("relative", containerClassName)}>
        {label && (
          <label
            htmlFor={id}
            className="mb-[3px] block text-[15px] leading-[18px] text-fore-neutral-secondary"
          >
            {label}
          </label>
        )}
        <select
          id={id}
          className={cn(
            "h-[41px] border border-border rounded-[4px] min-w-[230px] border-stroke-neutral-decorative bg-back-neutral-primary pl-[10px] w-[100%] text-fore-neutral-secondary outline-none",
            className
          )}
          value={value}
          onChange={onChange}
          disabled={disabled}
          ref={ref}
          {...rest}
        >
          <option value="config">{placeholder}</option>
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {!disableError && (
          <span className="mt-[3px] block h-[14px] text-[12px] text-status-error">
            {error}
          </span>
        )}
      </div>
    );
  }
);

ModeInput.displayName = "ModeInput";
