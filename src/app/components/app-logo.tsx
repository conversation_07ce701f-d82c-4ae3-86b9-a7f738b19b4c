export const AppLogo = () => {
  return (
    <svg
      width="181"
      height="25"
      viewBox="0 0 181 25"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M25.6349 7.24251C25.6349 8.65074 25.6628 10.0603 25.6153 11.4676C25.595 11.8406 25.4443 12.1957 25.1877 12.4748C21.3146 16.3414 17.4263 20.1943 13.5223 24.0337C12.9175 24.6314 12.8584 24.6291 12.2364 24.0132C8.3948 20.2133 4.55065 16.4148 0.703912 12.6178C0.517352 12.45 0.370385 12.2453 0.273083 12.0176C0.17578 11.7899 0.130356 11.5446 0.139922 11.2984C0.165198 8.54995 0.149108 5.80102 0.165199 3.0521C0.198647 2.79356 0.264971 2.53999 0.362654 2.29715C0.627828 2.39375 0.877738 2.52558 1.10528 2.68883C2.05778 3.59788 3.0016 4.51805 3.90355 5.47373C4.14041 5.73914 4.28193 6.07164 4.30676 6.4214C4.35592 7.53169 4.29669 8.64814 4.34496 9.7592C4.36289 10.1115 4.50238 10.4479 4.7412 10.7145C7.28596 13.2635 9.8658 15.7802 12.4173 18.3235C12.7994 18.7045 13.0425 18.6477 13.3903 18.3017C15.9455 15.762 18.521 13.2413 21.0657 10.6914C21.2829 10.4366 21.4052 10.1186 21.4128 9.78853C21.4546 8.67829 21.4052 7.56229 21.4489 6.45026C21.4622 6.11955 21.5876 5.80223 21.8056 5.547C22.7767 4.52914 23.789 3.54859 24.8045 2.57159C24.9837 2.44731 25.182 2.35068 25.3923 2.28516C25.4994 2.47044 25.5741 2.67126 25.6147 2.87979C25.6337 4.3333 25.6248 5.78725 25.6248 7.24118H25.6349"
        fill="var(--logoColor)"
      />
      <path
        d="M12.9249 0.479393C15.8821 0.479393 18.8385 0.473619 21.7962 0.488274C22.1054 0.520466 22.4113 0.58448 22.7072 0.679233C22.5703 0.970209 22.4006 1.24573 22.2017 1.5008C21.2692 2.45915 20.3215 3.40462 19.3478 4.32389C19.1685 4.48283 19.0279 4.67897 18.938 4.89767C18.8474 5.11636 18.8094 5.35196 18.8265 5.58688C18.8588 6.74154 18.8619 7.90234 18.8208 9.05793C18.8081 9.38848 18.6808 9.70541 18.4603 9.95804C16.7945 11.6505 15.0878 13.3056 13.4122 14.9896C13.0247 15.3791 12.7419 15.3578 12.3668 14.9812C10.7051 13.3154 9.01399 11.6767 7.3665 9.99802C7.13281 9.72891 6.99832 9.39222 6.98439 9.04102C6.93852 7.88637 6.94904 6.72641 6.97704 5.56957C6.99262 5.34583 6.95582 5.1216 6.86948 4.91348C6.78313 4.70537 6.64947 4.51873 6.4783 4.36741C5.50426 3.44948 4.55721 2.50223 3.62536 1.54388C3.40865 1.27349 3.22051 0.982784 3.06372 0.676125C3.3868 0.583415 3.71836 0.520955 4.05385 0.489607C7.01106 0.474952 9.96877 0.479393 12.9249 0.479393Z"
        fill="var(--logoColor)"
      />
      <path
        d="M54.3264 11.7168C54.3235 12.6737 53.9291 13.5905 53.2294 14.267C52.5298 14.9436 51.5816 15.3249 50.5921 15.3278H48.4951L54.3264 22.0497V22.4622H50.6493L44.4443 15.3236H37.3776C37.1655 15.3202 36.9539 15.3018 36.7445 15.2684V22.4622H33.6433V2.49438H50.5921C51.5816 2.49722 52.5298 2.87859 53.2294 3.55512C53.9291 4.23173 54.3235 5.14849 54.3264 6.10535V11.7168ZM50.5921 12.3273C50.7591 12.3259 50.9188 12.261 51.0368 12.1469C51.155 12.0326 51.222 11.8783 51.2235 11.7168V6.10697C51.2221 5.94533 51.1552 5.79074 51.0372 5.67642C50.9191 5.56202 50.7593 5.49712 50.5921 5.49566H37.3776C37.2102 5.49696 37.0501 5.56169 36.9317 5.6761C36.8133 5.79042 36.7461 5.94517 36.7445 6.10697V11.7168C36.7463 11.8785 36.8136 12.033 36.932 12.1472C37.0504 12.2613 37.2104 12.326 37.3776 12.3273H50.5921Z"
        fill="var(--logoColor)"
      />
      <path
        d="M81.2853 5.47898H65.4284V10.9791H78.1831V13.9787H65.4284V19.4789H81.2853V22.4785H62.3264V2.47852H81.2853V5.47898Z"
        fill="var(--logoColor)"
      />
      <path
        d="M93.0194 5.47818C92.8522 5.47956 92.6924 5.54454 92.5741 5.65886C92.4559 5.77318 92.3888 5.92777 92.3872 6.08941V18.8675C92.3888 19.0292 92.4559 19.1838 92.5741 19.2981C92.6924 19.4124 92.8522 19.4773 93.0194 19.4788H109.912V22.4785H93.0194C92.5288 22.4793 92.0428 22.3866 91.5893 22.2054C91.1359 22.0242 90.7239 21.7583 90.3769 21.4228C90.03 21.0873 89.7549 20.6889 89.5676 20.2503C89.3802 19.8119 89.2843 19.342 89.2852 18.8675V6.08941C89.2843 5.61494 89.3802 5.1451 89.5676 4.70661C89.7549 4.26804 90.03 3.86966 90.3769 3.53415C90.7239 3.19864 91.1359 2.93273 91.5893 2.75156C92.0428 2.57048 92.5288 2.47763 93.0194 2.47852H109.912V5.47818H93.0194Z"
        fill="var(--logoColor)"
      />
      <path
        d="M134.861 2.47852C135.352 2.47763 135.837 2.57048 136.291 2.75156C136.744 2.93273 137.156 3.19864 137.503 3.53415C137.849 3.86966 138.125 4.26804 138.312 4.70661C138.5 5.1451 138.596 5.61494 138.595 6.08941V18.8675C138.596 19.342 138.5 19.8119 138.312 20.2503C138.125 20.6889 137.849 21.0873 137.503 21.4228C137.156 21.7583 136.744 22.0242 136.291 22.2054C135.837 22.3866 135.352 22.4793 134.861 22.4785H121.646C121.155 22.4793 120.669 22.3866 120.216 22.2054C119.762 22.0242 119.351 21.7583 119.003 21.4228C118.656 21.0873 118.382 20.6889 118.194 20.2503C118.006 19.8119 117.911 19.342 117.912 18.8675V6.08941C117.911 5.61494 118.006 5.1451 118.194 4.70661C118.382 4.26804 118.656 3.86966 119.003 3.53415C119.351 3.19864 119.762 2.93273 120.216 2.75156C120.669 2.57048 121.155 2.47763 121.646 2.47852H134.861ZM134.861 19.478C135.028 19.4765 135.188 19.4116 135.306 19.2973C135.424 19.183 135.491 19.0284 135.492 18.8667V6.08941C135.491 5.92777 135.424 5.77318 135.306 5.65886C135.188 5.54454 135.028 5.47956 134.861 5.47818H121.646C121.478 5.47956 121.319 5.54454 121.2 5.65886C121.082 5.77318 121.015 5.92777 121.014 6.08941V18.8675C121.015 19.0292 121.082 19.1838 121.2 19.2981C121.319 19.4124 121.478 19.4773 121.646 19.4788L134.861 19.478Z"
        fill="var(--logoColor)"
      />
      <path
        d="M164.176 2.47876H167.281V22.4779H163.603L149.696 6.47822V22.4779H146.595V2.47876H150.272L164.178 18.4784L164.176 2.47876Z"
        fill="var(--logoColor)"
      />
    </svg>
  );
};
