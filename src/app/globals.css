@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  min-height: 100vh;
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;

  /* Design System Color Tokens - Light Theme */
  /* Accent Colors */
  --fill-accent-primary: #7160e8;
  --fill-accent-secondary: #5548ae;
  --fill-accent-tertiary: #443a8b;
  --fill-accent-alt-primary: #dfdbfa;
  --fill-accent-alt-secondary: #d2ceed;
  --fill-accent-alt-tertiary: #c2bee1;

  /* Foreground Colors */
  --fore-on-accent-primary: #ffffff;
  --fore-on-accent-secondary: #ffffff;
  --fore-on-accent-tertiary: #ffffff;
  --fore-neutral-primary: #000000;
  --fore-neutral-secondary: rgba(0, 0, 0, 0.8);
  --fore-neutral-tertiary: rgba(0, 0, 0, 0.75);
  --fore-neutral-quaternary: rgba(0, 0, 0, 0.6);

  /* Background Colors */
  --back-accent-primary: #f1f0ff;
  --back-accent-secondary: #e8e6f7;
  --back-accent-tertiary: #dddbf2;
  --back-accent-quaternary: #f9f8fe;
  --back-neutral-primary: #fafafa;
  --back-neutral-secondary: #f5f5f5;
  --back-neutral-tertiary: #ededed;

  /* Stroke Colors */
  --stroke-neutral-decorative: rgba(0, 0, 0, 0.1);

  /* Status Colors */
  --status-success: #259465;
  --status-error: #ff5252;

  /* Typography Tokens */
  /* Highlight Styles */
  --typo-highlight-1-size: 54px;
  --typo-highlight-1-line-height: 64px;
  --typo-highlight-1-weight: 700;

  --typo-highlight-2-size: 40px;
  --typo-highlight-2-line-height: 48px;
  --typo-highlight-2-weight: 700;

  --typo-highlight-3-size: 36px;
  --typo-highlight-3-line-height: 48px;
  --typo-highlight-3-weight: 700;

  --typo-highlight-4-size: 40px;
  --typo-highlight-4-line-height: 48px;
  --typo-highlight-4-weight: 700;

  --typo-highlight-5-size: 36px;
  --typo-highlight-5-line-height: 48px;
  --typo-highlight-5-weight: 700;

  --typo-highlight-6-size: 24px;
  --typo-highlight-6-line-height: 32px;
  --typo-highlight-6-weight: 700;

  /* Heading Styles */
  --typo-heading-size: 18px;
  --typo-heading-line-height: 22px;
  --typo-heading-weight: 500;

  --typo-heading-strong-size: 18px;
  --typo-heading-strong-line-height: 22px;
  --typo-heading-strong-weight: 700;

  /* Title Styles */
  --typo-title-3-strong-size: 20px;
  --typo-title-3-strong-line-height: 26px;
  --typo-title-3-strong-weight: 700;

  --typo-title-2-strong-size: 16px;
  --typo-title-2-strong-line-height: 22px;
  --typo-title-2-strong-weight: 700;

  --typo-title-1-strong-size: 14px;
  --typo-title-1-strong-line-height: 22px;
  --typo-title-1-strong-weight: 700;

  /* Body Styles */
  --typo-body-4-size: 18px;
  --typo-body-4-line-height: 28px;
  --typo-body-4-weight: 500;

  --typo-body-4-strong-size: 18px;
  --typo-body-4-strong-line-height: 28px;
  --typo-body-4-strong-weight: 700;

  --typo-body-3-size: 16px;
  --typo-body-3-line-height: 26px;
  --typo-body-3-weight: 500;

  --typo-body-3-strong-size: 16px;
  --typo-body-3-strong-line-height: 26px;
  --typo-body-3-strong-weight: 700;

  --typo-body-2-size: 14px;
  --typo-body-2-line-height: 22px;
  --typo-body-2-weight: 500;

  --typo-body-2-strong-size: 14px;
  --typo-body-2-strong-line-height: 22px;
  --typo-body-2-strong-weight: 700;

  --typo-body-1-size: 13px;
  --typo-body-1-line-height: 20px;
  --typo-body-1-weight: 500;

  --typo-body-1-strong-size: 13px;
  --typo-body-1-strong-line-height: 20px;
  --typo-body-1-strong-weight: 700;

  /* Attribution Styles */
  --typo-attribution-size: 11px;
  --typo-attribution-line-height: 13px;
  --typo-attribution-weight: 500;

  --typo-attribution-strong-size: 11px;
  --typo-attribution-strong-line-height: 13px;
  --typo-attribution-strong-weight: 900;

  /* Font Family */
  --typo-font-family: var(--font-blender-pro), -apple-system, BlinkMacSystemFont,
    "Segoe UI", Roboto, sans-serif;

  /* Button State Colors - Light Theme */
  /* Primary Button States */
  --button-primary-default-bg: #7160e8;
  --button-primary-default-text: #ffffff;
  --button-primary-hover-bg: #5548ae;
  --button-primary-hover-text: #ffffff;
  --button-primary-pressed-bg: #443a8b;
  --button-primary-pressed-text: #ffffff;

  /* Secondary Button States */
  --button-secondary-default-bg: #dfdbfa;
  --button-secondary-default-text: #7160e8;
  --button-secondary-hover-bg: #d2ceed;
  --button-secondary-hover-text: #403784;
  --button-secondary-pressed-bg: #c2bee1;
  --button-secondary-pressed-text: #342c6a;

  /* Outline Button States */
  --button-outline-default-bg: transparent;
  --button-outline-default-text: #7160e8;
  --button-outline-default-border: #7160e8;
  --button-outline-hover-bg: transparent;
  --button-outline-hover-text: #403784;
  --button-outline-hover-border: #5548ae;
  --button-outline-pressed-bg: #dddbf2;
  --button-outline-pressed-text: #342c6a;
  --button-outline-pressed-border: #5548ae;

  /* Gradient Colors */
  --gradient-primary-start: #5100ff;
  --gradient-primary-end: #ffffff;
  --gradient-secondary-start: #5c25d2;
  --gradient-secondary-end: #4700de;
  --gradient-info-start: rgba(30, 13, 66, 0.67);
  --gradient-info-end: rgba(23, 23, 23, 0.67);
  --gradient-dark-start: #303030;
  --gradient-dark-end: #1e1e1e;

  /* Legacy colors for backward compatibility - DEPRECATED */
  /* These are kept for backward compatibility but should not be used in new code */
  --primary-color: var(--fill-accent-primary);
  --secondary-color: var(--back-neutral-secondary);
  --accent-color: var(--fill-accent-alt-primary);
  --danger-color: #f54f4f;
  --success-color: #41d98d;
  --light-text-color: var(--fore-on-accent-primary);
  --dark-text-color: var(--fore-neutral-primary);
  --primary: var(--fill-accent-primary);
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 0, 0, 0;
    --background-start-rgb: 214, 219, 220;
    --background-end-rgb: 255, 255, 255;
  }
}

.light {
  /* Design System Color Tokens - Light Theme Override */
  /* Accent Colors */
  --fill-accent-primary: #7160e8;
  --fill-accent-secondary: #5548ae;
  --fill-accent-tertiary: #443a8b;
  --fill-accent-alt-primary: #dfdbfa;
  --fill-accent-alt-secondary: #d2ceed;
  --fill-accent-alt-tertiary: #c2bee1;

  /* Foreground Colors */
  --fore-on-accent-primary: #ffffff;
  --fore-on-accent-secondary: #ffffff;
  --fore-on-accent-tertiary: #ffffff;
  --fore-neutral-primary: #000000;
  --fore-neutral-secondary: rgba(0, 0, 0, 0.8);
  --fore-neutral-tertiary: rgba(0, 0, 0, 0.75);
  --fore-neutral-quaternary: rgba(0, 0, 0, 0.6);

  /* Background Colors */
  --back-accent-primary: #f1f0ff;
  --back-accent-secondary: #e8e6f7;
  --back-accent-tertiary: #dddbf2;
  --back-accent-quaternary: #f9f8fe;
  --back-neutral-primary: #fafafa;
  --back-neutral-secondary: #f5f5f5;
  --back-neutral-tertiary: #ededed;

  /* Stroke Colors */
  --stroke-neutral-decorative: rgba(0, 0, 0, 0.1);

  /* Status Colors */
  --status-success: #259465;
  --status-error: #ff5252;

  /* Button State Colors - Light Theme */
  /* Primary Button States */
  --button-primary-default-bg: #7160e8;
  --button-primary-default-text: #ffffff;
  --button-primary-hover-bg: #5548ae;
  --button-primary-hover-text: #ffffff;
  --button-primary-pressed-bg: #443a8b;
  --button-primary-pressed-text: #ffffff;

  /* Secondary Button States */
  --button-secondary-default-bg: #dfdbfa;
  --button-secondary-default-text: #7160e8;
  --button-secondary-hover-bg: #d2ceed;
  --button-secondary-hover-text: #403784;
  --button-secondary-pressed-bg: #c2bee1;
  --button-secondary-pressed-text: #342c6a;

  /* Outline Button States */
  --button-outline-default-bg: transparent;
  --button-outline-default-text: #7160e8;
  --button-outline-default-border: #7160e8;
  --button-outline-hover-bg: transparent;
  --button-outline-hover-text: #403784;
  --button-outline-hover-border: #5548ae;
  --button-outline-pressed-bg: #dddbf2;
  --button-outline-pressed-text: #342c6a;
  --button-outline-pressed-border: #5548ae;

  /* Gradient Colors - Light Theme */
  --gradient-primary-start: #5100ff;
  --gradient-primary-end: #ffffff;
  --gradient-secondary-start: #5c25d2;
  --gradient-secondary-end: #4700de;
  --gradient-info-start: rgba(30, 13, 66, 0.67);
  --gradient-info-end: rgba(23, 23, 23, 0.67);
  --gradient-dark-start: #303030;
  --gradient-dark-end: #1e1e1e;

  /* Legacy theme variables */
  --primary: var(--fill-accent-primary);
  --primary-bg: var(--back-accent-secondary);
  --text-secondary: var(--fore-neutral-secondary);
  --grey-secondary: var(--fore-neutral-quaternary);
  --block-bg: var(--back-neutral-tertiary);
  --aside: var(--fore-on-accent-primary);
  --input-bg: var(--back-neutral-primary);
  --divider: var(--stroke-neutral-decorative);
  --success: var(--status-success);
  --error: var(--status-error);
  --text-primary: var(--fore-neutral-primary);
  --dashboard-bg: var(--back-neutral-secondary);
  --logoColor: var(--fill-accent-primary);
}

.dark {
  /* Design System Color Tokens - Dark Theme */
  /* Accent Colors */
  --fill-accent-primary: #dfdbfa;
  --fill-accent-secondary: #c9c4e8;
  --fill-accent-tertiary: #b8b3d4;
  --fill-accent-alt-primary: #343147;
  --fill-accent-alt-secondary: #3b384f;
  --fill-accent-alt-tertiary: #413d56;

  /* Foreground Colors */
  --fore-on-accent-primary: #5649b0;
  --fore-on-accent-secondary: #3626a4;
  --fore-on-accent-tertiary: #291d7b;
  --fore-neutral-primary: #fafafa;
  --fore-neutral-secondary: rgba(255, 255, 255, 0.8);
  --fore-neutral-tertiary: rgba(255, 255, 255, 0.75);
  --fore-neutral-quaternary: rgba(255, 255, 255, 0.6);

  /* Background Colors */
  --back-accent-primary: #272533;
  --back-accent-secondary: #2b2938;
  --back-accent-tertiary: #2f2d3d;
  --back-accent-quaternary: #1a1f23;
  --back-neutral-primary: #1b1a19;
  --back-neutral-secondary: #262626;
  --back-neutral-tertiary: #2a2a2a;

  /* Stroke Colors */
  --stroke-neutral-decorative: rgba(255, 255, 255, 0.1);

  /* Status Colors */
  --status-success: #259465;
  --status-error: #ff5252;

  /* Button State Colors - Dark Theme */
  /* Primary Button States */
  --button-primary-default-bg: #7160e8;
  --button-primary-default-text: #ffffff;
  --button-primary-hover-bg: #5548ae;
  --button-primary-hover-text: #ffffff;
  --button-primary-pressed-bg: #443a8b;
  --button-primary-pressed-text: #ffffff;

  /* Secondary Button States */
  --button-secondary-default-bg: #343147;
  --button-secondary-default-text: #dfdbfa;
  --button-secondary-hover-bg: #343147;
  --button-secondary-hover-text: #dfdbfa;
  --button-secondary-pressed-bg: #413d56;
  --button-secondary-pressed-text: #b8b3d4;

  /* Outline Button States */
  --button-outline-default-bg: transparent;
  --button-outline-default-text: #dfdbfa;
  --button-outline-default-border: #dfdbfa;
  --button-outline-hover-bg: transparent;
  --button-outline-hover-text: #c9c4e8;
  --button-outline-hover-border: #c9c4e8;
  --button-outline-pressed-bg: #413d56;
  --button-outline-pressed-text: #b8b3d4;
  --button-outline-pressed-border: #c9c4e8;

  /* Gradient Colors - Dark Theme */
  --gradient-primary-start: #dfdbfa;
  --gradient-primary-end: #5649b0;
  --gradient-secondary-start: #343147;
  --gradient-secondary-end: #413d56;
  --gradient-info-start: rgba(39, 37, 51, 0.67);
  --gradient-info-end: rgba(43, 41, 56, 0.67);
  --gradient-dark-start: var(--gradient-dark-start);
  --gradient-dark-end: var(--gradient-dark-end);

  /* Legacy theme variables */
  --primary: var(--fill-accent-primary);
  --primary-bg: var(--back-accent-primary);
  --text-secondary: var(--fore-neutral-secondary);
  --grey-secondary: var(--fore-neutral-quaternary);
  --block-bg: linear-gradient(
    95.95deg,
    var(--gradient-dark-start) 35.62%,
    var(--gradient-dark-end) 113.48%
  );
  --aside: var(--back-neutral-secondary);
  --input-bg: var(--back-neutral-primary);
  --divider: var(--stroke-neutral-decorative);
  --success: var(--status-success);
  --error: var(--status-error);
  --text-primary: var(--fore-neutral-primary);
  --dashboard-bg: var(--back-neutral-primary);
  --logoColor: var(--fore-neutral-primary);
}
.app-button-default {
  background: var(--fore-on-accent-primary);
  color: var(--fore-neutral-primary);
}

.landing-info-block {
  background: linear-gradient(
    288deg,
    var(--gradient-info-start) -21.63%,
    var(--gradient-info-end) 92%
  );
}

.landing-footer-block {
  background: linear-gradient(
    96.89deg,
    var(--gradient-secondary-start) 33.34%,
    var(--gradient-secondary-end) 122.54%
  );
}

.dark .gradient-dark-bg {
  background-image: linear-gradient(
    95.95deg,
    var(--gradient-dark-start) 35.62%,
    var(--gradient-dark-end) 113.48%
  );
}

.aside-menu::-webkit-scrollbar {
  display: none;
}
.aside-menu {
  position: sticky;
  top: 0;
  max-height: 100vh;
  overflow-y: scroll;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.main-container {
  background-color: var(--back-neutral-primary);
}

.main-title-custom {
  background: linear-gradient(
    277.21deg,
    var(--gradient-primary-start) -13.33%,
    var(--gradient-primary-end) 51.57%
  );
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* DEPRECATED: Use createGradientTextStyle utility from gradient-text-utils.ts instead */
.sub-title-custom {
  background: linear-gradient(
    277.04deg,
    var(--gradient-primary-start) 16.04%,
    var(--gradient-primary-end) 51.63%
  );
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 40px;
  display: inline-block;
}

/*****************************
* PREVIOUS STYLE *
* DELETE WHEN MERGING INTO MAIN *
*****************************/

.main-title-custom-prev {
  background: linear-gradient(
    277.21deg,
    var(--gradient-primary-start) -13.33%,
    var(--gradient-primary-end) 51.57%
  );
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.landing-info-block-prev {
  background: linear-gradient(
    288deg,
    var(--gradient-info-start) -21.63%,
    var(--gradient-info-end) 92%
  );
}

.landing-footer-block-prev {
  background: linear-gradient(
    96.89deg,
    var(--gradient-secondary-start) 33.34%,
    var(--gradient-secondary-end) 122.54%
  );
}

.gradient-bg-prev {
  width: 100vw;
  min-height: 100vh;
  overflow: hidden;
  background: linear-gradient(
    40deg,
    var(--back-neutral-primary),
    var(--back-accent-primary)
  );
  top: 0;
  left: 0;
  z-index: 0;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Custom Scrollbar Styles */
/* Webkit browsers (Chrome, Safari, Edge) */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--back-neutral-tertiary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--stroke-neutral-decorative);
  border-radius: 4px;
  border: 1px solid var(--back-neutral-tertiary);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--fore-neutral-quaternary);
}

::-webkit-scrollbar-thumb:active {
  background: var(--fore-neutral-tertiary);
}

::-webkit-scrollbar-corner {
  background: var(--back-neutral-tertiary);
}

/* Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--stroke-neutral-decorative) var(--back-neutral-tertiary);
}

/* Custom scrollbar for specific containers */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: var(--fill-accent-primary);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: var(--fill-accent-secondary);
}

.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: var(--fill-accent-primary) var(--back-neutral-tertiary);
}

/* Theme Switch Enhancements */
.theme-switch-container {
  position: relative;
  overflow: hidden;
}

.theme-switch-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    var(--fore-neutral-quaternary) 50%,
    transparent 70%
  );
  transform: translateX(-100%);
  transition: transform 0.6s ease-in-out;
}

.theme-switch-container:hover::before {
  transform: translateX(100%);
}

/* Smooth backdrop blur for sticky header */
.sticky-header-blur {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Theme icon active state animation */
@keyframes theme-icon-pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.theme-icon-active {
  animation: theme-icon-pulse 2s ease-in-out infinite;
}

.aurora-container {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
}