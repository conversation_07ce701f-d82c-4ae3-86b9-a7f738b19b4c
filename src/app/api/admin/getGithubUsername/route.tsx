import axios from "axios";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import type { JWT } from "next-auth/jwt";
import { getToken } from "next-auth/jwt";

const secret = process.env.NEXTAUTH_SECRET;

// Get all jobs running for the organization that the user belongs to
export async function POST(req: NextRequest) {
  const sesh = await getServerSession();
  if (!sesh) {
    return NextResponse.json({ error: "Need Log in" }, { status: 401 });
  }

  const { userId } = await req.json();

  // It's guaranteed to be there due to the check above
  const token: JWT = (await getToken({ req, secret })) as JWT;
  let ghData;
  try {
    const response = await axios({
      method: 'GET',
      url: `https://api.github.com/user/${userId}`,
      headers: {
        'Accept': 'application/vnd.github.v3+json',
        'Authorization': `Bearer ${token.access_token}`
      }
    });
    ghData = response.data;
  } catch (e) {
    // Axios error handling
    if (e?.response?.data) {
      return NextResponse.json(
        { data: {}, message: e.response.data.message },
        { status: e.response.status },
      );
    } else {
      return NextResponse.json(
        { data: {}, message: "Something went wrong" },
        { status: 500 },
      );
    }
  }

  // Returns an object with {data, message}
  return NextResponse.json({ data: ghData, message:"success"}, {status: 200});
}
