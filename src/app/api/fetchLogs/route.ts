import axios from "axios";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";

export async function POST(req: NextRequest) {
  let localLogsUrl: string;
  try {
    const body = await req.json();
    const { logsUrl } = body;
    localLogsUrl = logsUrl;
  } catch (e) {
    return NextResponse.json(
      { data: {}, message: "Invalid request body" },
      { status: 500 }
    );
  }
  
  //TODO 0XSI
  // Remove for prod
  // Medusa
  // localLogsUrl = "https://assets.getrecon.xyz/job/7358e6f0-7dfc-4d0b-9219-f786a5d48e05/logs/final.txt?Expires=1718188734&Key-Pair-Id=K15QZNH7H5D0KP&Signature=cRwPiqOKePP~u650cxr4Ea2SCAEsltT5RJX3olNTLXCW0gdUAfQBI2ib1ta-tvtM7wFDF1ChgbjlpNgMLSy7BR9JekOQgdNMD6b8ZY6LszsgfsTL-QEMaf63eODqPeWB3O5zvzbpi9R36Ig3qLHPW3MuPzC2EKvAkLlSV5wQkF4aw6SbSizcVgQla9-GycrbAmY0parmsGjLc0rzUMvqYL9Ko2vn4WwaAIsaDNSeRM3OUFqyu6PuL8KdbMhYKfjdlftlAEnBt~vqB8D~uJUw9rR2KTXk9pG5aH2GQdoHwLzceuhhc1tzwIzjHzocSdc1wQ-xBJMcZlrPjebMqPYVtw__";
  // Echidna
  // localLogsUrl = "https://assets.getrecon.xyz/job/98833ac8-d537-434a-ac4e-c1e26a7b5fda/logs/final.txt?Expires=1718256164&Key-Pair-Id=K15QZNH7H5D0KP&Signature=Im9Fdl1KlVmNJYK3BQFlCZghTnvYmY5l1U~UClKzfMStwHrRFuP1DBMefVckpfpEyHdmFoK6G4GgKIADn8YbhcgOCiTaJ9sIRZlMIcFE55mOnvxS8VPznxIaWTXE-eAkr1eSmjuFQlOidgfsUzgkQuKWhuBV-Wd8nzPaq4X~JlQ29DmZOZO86w27NuMBeVEccvy0U37KahptzVXOEuQXSJbdvTCZlwGJUc~pITq7EbuEsn2u3Ks5vUTFkh3qwazI5HKXD1P4alj~o-u4I9v~fIRFqmYeqx97ox5MjcddsM-28ll5jnpRPayAZPa6usZIG1oS9FsPl~1HK8thE10nsw__"
  const fetchLogs = await axios({
    method: "GET",
    url: localLogsUrl,
  });

  if (fetchLogs.status !== 200) {
    return NextResponse.json(
      { data: {}, message: "Error fetching logs" },
      { status: 500 }
    );
  } else {
    return NextResponse.json(fetchLogs.data);
  }
}
