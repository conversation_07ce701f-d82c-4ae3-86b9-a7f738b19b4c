import axios from "axios";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import type { JWT } from "next-auth/jwt";
import { getToken } from "next-auth/jwt";

const secret = process.env.NEXTAUTH_SECRET;

// Get all jobs running for the organization that the user belongs to
export async function POST(req: NextRequest) {
  const sesh = await getServerSession();
  if (!sesh) {
    return NextResponse.json({ error: "Need Log in" }, { status: 401 });
  }

  // It's guaranteed to be there due to the check above
  const token: JWT = (await getToken({ req, secret })) as JWT;
  const body = await req.json();
  const { telegramUsername } = body;

  let foundData;
  try {
    foundData = await axios({
      method: "POST",
      url: `${process.env.BACKEND_API_URL}/telegram/testChat`,
      headers: { Authorization: `Bearer ${token.access_token}` },
      data: {
        username: telegramUsername,
      }
    });
  } catch (e) {
    // Axios error handling
    if (e?.response?.data) {
      return NextResponse.json(
        { data: {}, message: e.response.data.message },
        { status: e.response.status }
      );
    } else {
      return NextResponse.json(
        { data: {}, message: "Something went wrong" },
        { status: 500 }
      );
    }
  }

  // Returns an object with {data, message}
  return NextResponse.json(foundData.data);
}
