import axios from "axios";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import type { JWT } from "next-auth/jwt";
import { getToken } from "next-auth/jwt";

const secret = process.env.NEXTAUTH_SECRET;

// TODO 0XSI
// Probably safe to deprecate this route as brokenProps are coming in with the jobs and shares now
// Get all broken properties for a job
export async function POST(req: NextRequest) {
  const sesh = await getServerSession();
  if (!sesh) {
    return NextResponse.json({ error: "Need Log in" }, { status: 401 });
  }
  const body = await req.json();
  const { jobId } = body;

  // It's guaranteed to be there due to the check above
  const token: JWT = (await getToken({ req, secret })) as JWT;

  let brokenProperties;
  try {
    brokenProperties = await axios({
      method: "GET",
      url: `${process.env.BACKEND_API_URL}/jobs/brokenProperty/${jobId}`,
      headers: { Authorization: `Bearer ${token.access_token}` },
    });
    return NextResponse.json(brokenProperties.data);
  } catch (e) {
    // Axios error handling
    if (e?.response?.data) {
      return NextResponse.json(
        { data: {}, message: e.response.data.message },
        { status: e.response.status },
      );
    } else {
      return NextResponse.json(
        { data: {}, message: "Something went wrong" },
        { status: 500 },
      );
    }
  }

  return NextResponse.json(brokenProperties.data);
}
