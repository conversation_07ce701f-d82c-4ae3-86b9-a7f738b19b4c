import axios from "axios";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import type { JWT } from "next-auth/jwt";
import { getToken } from "next-auth/jwt";

const secret = process.env.NEXTAUTH_SECRET;

export async function POST(req: NextRequest) {
  const sesh = await getServerSession();
  if (!sesh) {
    return NextResponse.json({ error: "Need Log in" }, { status: 401 });
  }

  const token: JWT = await getToken({ req, secret });
  const { id } = await req.json();
  try {
    const foundData = await axios({
      method: "PUT",
      url: `${process.env.BACKEND_API_URL}/governanceFuzzing/toggle/${id}`,
      headers: { Authorization: `Bearer ${token.access_token}` },
    });
    return NextResponse.json(foundData.data);
  } catch (e) {
    console.log(e)
    return NextResponse.json({ error: "Something went wrong" }, { status: 500 });
  }
}

/*
TESTING
{
  "address": "******************************************",
  "topic": "0x91310dcc85f7af09d439ec3a1ce13f09085e5d930be7a1f102bf6a0baf606bb8",
  "eventDefinition": "Trigger_Multi(address indexed,uint256,uint256,bool)",
  "chain": 11155111,
  "recipeID": "ac43765c-ba09-44d8-a162-7ad18be45cc2"
}


Think about a way to show the 

*/
