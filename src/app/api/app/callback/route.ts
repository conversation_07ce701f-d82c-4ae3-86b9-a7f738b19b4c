import { redirect } from "next/navigation";
import type { NextRequest, NextResponse } from "next/server";

export async function GET(req: NextRequest) {
  const { installation_id, setup_action } = Object.fromEntries(
    req.nextUrl.searchParams,
  );
  // console.log({ installation_id, setup_action });
  // TODO: If this is an installation we should signal to the backend
  // If this is OAUTH, we could create user if we want
  // TODO: This could also be useful to check if user has pending invites

  // TODO: I guess this helps us get a callback for installations
  // So we can verify it, and update DB
  // Although I would prefer webhook

  redirect("/");
}
