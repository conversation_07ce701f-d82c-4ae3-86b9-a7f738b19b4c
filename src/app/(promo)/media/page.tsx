"use client";

import { AppVideoCarousel } from "../../components/AppVideosCarousel/AppVideosCarousel";
import PromoNavbar from "../lp-components/PromoNavbar";

const MEDIA_VIDEOS = [
  {
    title: "The Dangers of Arbitrary Calls and How to do them safely",
    url: "https://www.youtube.com/watch?v=8-qWL2Dcgpc",
    duration: "42min",
    thumbnail: "https://img.youtube.com/vi/8-qWL2Dcgpc/maxresdefault.jpg",
  },
  {
    title:
      "Eigenlayer Ecosystem Fuzzing - Write Invariant Tests for Eigenlayer in just a few minutes",
    url: "https://www.youtube.com/watch?v=WDdFVZpTAZo",
    duration: "32min",
    thumbnail: "https://img.youtube.com/vi/WDdFVZpTAZo/maxresdefault.jpg",
  },
  {
    title: "The Recon Pro Workflow",
    url: "https://www.youtube.com/watch?v=DHAvBrsITRU",
    duration: "44min",
    thumbnail: "https://img.youtube.com/vi/DHAvBrsITRU/maxresdefault.jpg",
  },
  {
    title: "Invariant Testing of Blast Shares Logic from the Blast Contest",
    url: "https://www.youtube.com/watch?v=XCEICO6uGrE",
    duration: "32min",
    thumbnail: "https://img.youtube.com/vi/XCEICO6uGrE/maxresdefault.jpg",
  },
  {
    title: "The future of Recon",
    url: "https://www.youtube.com/watch?v=WJLU2OJO430",
    duration: "34min",
    thumbnail: "https://img.youtube.com/vi/WJLU2OJO430/maxresdefault.jpg",
  },
  {
    title: "How to get started with Invariant Testing using Medusa",
    url: "https://www.youtube.com/watch?v=WasI2-DWceM",
    duration: "42min",
    thumbnail: "https://img.youtube.com/vi/WasI2-DWceM/maxresdefault.jpg",
  },
];

export default function Home() {
  return (
    <div>
      <PromoNavbar />
      <div className="main-container flex w-full flex-col items-center overflow-x-hidden p-4 pt-[80px] lg:p-10 lg:pt-[180px]">
        <h3 className=" sub-title-custom w-[350px] text-center font-bold leading-[48px] tracking-normal lg:text-[100px] lg:leading-[100px]">
          MEDIA
        </h3>
        <AppVideoCarousel videos={MEDIA_VIDEOS} />
      </div>
    </div>
  );
}
