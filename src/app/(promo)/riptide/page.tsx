"use client";

import Link from "next/link";
import { AppButton } from "../../components/app-button";
import Footer from "../../components/Footer/Footer";
import { LPGradientBackground } from "../../components/gradient-wrapper";
import BenefitsSection from "../lp-components/AuditBenefits/benefits-section";
import AuditsSection from "../lp-components/audits-section";
import CompanyLogos from "../lp-components/company-logos";
import PromoNavbar from "../lp-components/PromoNavbar";
import ReconProSection from "../lp-components/recon-pro-section";
import ServicesSection from "../lp-components/services-section";
import TeamSection from "../lp-components/team-section";
import Testimonials from "../lp-components/Testimonials/Testimonials";
import TrophiesSection from "../lp-components/trophies-section";
import TvlProtectedSection from "../lp-components/tvl-protected-section";

export default function Home() {
  return (
    <div>
      <PromoNavbar />
      <div className="main-container w-full overflow-x-hidden">
        <LPGradientBackground />
        <main className="relative z-10">
          <section className="mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center pt-[80px] lg:w-4/5 lg:pt-[180px]">
            <h1 className="main-title-custom mb-5 text-center text-[56px] font-bold leading-[48px] tracking-normal lg:text-[120px] lg:leading-[100px]">
              AUDITS POWERED BY INVARIANT TESTING
            </h1>
            <h2 className="mb-5 text-center font-thin tracking-normal text-white lg:my-[37px] lg:text-[37px] lg:leading-[33px]">
              World Class Audits powered by state of the art Invariant Tests
            </h2>
            <p className="mb-5 text-center font-thin tracking-normal text-white lg:my-[22px] lg:text-[22px] lg:leading-[22px]">
              $5k Discount on your first engagement by saying you were referred
              by &quot;RIPTIDE&quot;
            </p>
            <Link
              href="https://tally.so/r/w2Vqxb"
              className="m-0 flex flex-row items-center justify-center p-0 text-center"
              target="_blank"
              rel="noopener noreferrer"
            >
              <AppButton variant="secondary" className="m-0 p-0">
                Send Audit Request
              </AppButton>
            </Link>

            <CompanyLogos />
          </section>
          <TvlProtectedSection />

          <Testimonials />

          <BenefitsSection />

          <TrophiesSection />

          <AuditsSection />

          <TeamSection />

          <ServicesSection />

          <ReconProSection />
        </main>
        <div className="w-full border-t border-t-[#FFFFFF]">
          <Footer />
        </div>
      </div>
    </div>
  );
}
