import type { ReactNode } from "react";
import { H2, Body4 } from "../../components/app-typography";
import { createGradientTextStyle } from "./gradient-text-utils";

interface SectionWrapperProps {
  id?: string;
  title: string;
  subtitle?: string;
  children: ReactNode;
  className?: string;
  titleWidth?: string;
  titleAlign?: "left" | "center";
  containerWidth?: "full" | "4/5";
}

export default function SectionWrapper({
  id,
  title,
  subtitle,
  children,
  className = "",
  titleWidth,
  titleAlign = "left",
  containerWidth = "4/5",
}: SectionWrapperProps) {
  const containerClass =
    containerWidth === "full"
      ? "mx-auto mb-5 flex w-[87.5%] flex-col"
      : "mx-auto mb-5 flex w-[87.5%] flex-col lg:w-4/5";

  const titleAlignClass =
    titleAlign === "center"
      ? "text-center items-center justify-center"
      : "items-start justify-center";

  const titleWidthClass = titleWidth ? `w-[${titleWidth}]` : "";

  return (
    <section
      id={id}
      className={`${containerClass} ${titleAlignClass} pt-[30px] lg:pt-[180px] ${className}`}
    >
      <H2
        className={`${titleWidthClass} font-bold uppercase leading-[48px] tracking-normal lg:text-[100px] lg:leading-[100px] ${
          titleAlign === "center" ? "text-center" : "items-start"
        }`}
        style={createGradientTextStyle("primary", "40px")}
      >
        {title}
      </H2>
      {subtitle && (
        <Body4
          color="primary"
          className="mb-[40px] font-thin tracking-normal text-fore-on-accent-primary md:mb-5 lg:mb-[32px] lg:text-[32px] lg:leading-[33px]"
        >
          {subtitle}
        </Body4>
      )}
      {children}
    </section>
  );
}
