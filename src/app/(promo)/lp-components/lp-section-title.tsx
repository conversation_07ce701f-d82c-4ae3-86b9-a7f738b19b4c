import { cn } from "@/lib/utils";
import { ReactNode } from "react";

export const LPSectionTitle = ({
  children,
  className = "",
}: {
  children: ReactNode;
  className?: string;
}) => {
  return (
    <h3
      className={cn(
        "sub-title-custom items-start font-bold uppercase leading-[48px] tracking-normal lg:text-[100px] lg:leading-[100px]",
        className
      )}
    >
      {children}
    </h3>
  );
};
