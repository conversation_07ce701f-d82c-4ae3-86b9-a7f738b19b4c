import Trophy from "./Trophy";
import Caroussel from "../../../components/Caroussel/Caroussel";

const TrophyList = [
  {
    project: "Cap Money",
    severity: "Medium",
    findingName: "Agent health changes after realizeRestakerInterest",
    findingUrl:
      "https://github.com/Recon-Fuzz/cap-invariants/blob/main/README.md#m-01-med-agent-health-changes-after-realizerestakerinterest",
    reconLogs:
      "https://getrecon.xyz/shares/9e26f083-5fb1-40bd-987d-ce184d1f8676",
    description: "Fuzzing helped found a way to desynch accounting",
  },
  {
    project: "Spine Finance",
    severity: "Critical",
    findingName: "Permanent DOS and loss of funds when Y reaches 0",
    findingUrl: "https://github.com/GalloDaSballo/spine-review/issues/34",
    reconLogs:
      "https://getrecon.xyz/shares/c669e7f5-9650-4979-b0c8-863331a6737f/report",
    description:
      "Fuzzing with a Lossy Vault allowed to identify this edge case",
  },
  {
    project: "Badger",
    severity: "Critical",
    findingName: "Insolvency due to Incorrect Accounting",
    findingUrl:
      "https://gist.github.com/GalloDaSballo/564e16b9cfa9bcf998e3778fdb297045#critical",
    reconLogs:
      "https://getrecon.xyz/shares/76293e5b-de71-4636-b67b-501c32a7dc57",
    description: "remBADGER Accounting bug, prevented",
  },
  {
    project: "TapiocaDAO",
    severity: "High",
    findingName: "Overflow causes Permanent DOS in twTAP",
    findingUrl:
      "https://github.com/code-423n4/2024-02-tapioca-findings/issues/55",
    reconLogs:
      "https://getrecon.xyz/shares/d62b2bb7-98cf-400c-85af-b1cfb254a5e7",
    description: "The only researcher that wrote a POC",
  },
  {
    project: "Centrifuge",
    severity: "Medium",
    findingName: "Rounding errors allows bypassing caps",
    findingUrl:
      "https://getrecon.substack.com/p/lessons-learned-from-fuzzing-centrifuge-059",
    reconLogs:
      "https://getrecon.xyz/shares/bdafeee2-9a2c-45f4-90aa-420f5bc01ff6",
    description:
      "Invariant Tests helped prevent a notoriously hard to detect edge case",
  },
  {
    project: "Corn",
    severity: "Critical",
    findingName: "Insolvency due to Incorrect Accounting",
    findingUrl:
      "https://gist.github.com/GalloDaSballo/1810dbd7e4a339647fc66d190c1d1e51",
    reconLogs:
      "https://staging.getrecon.xyz/shares/9653d22a-e2e1-453c-8c67-b23912a578e3",
    description:
      "Invariant Testing quickly found a mistake in accounting and helped ensure all subsequent changes were safe",
  },
  {
    project: "Credit Coop",
    severity: "Medium",
    findingName: "Rounding allows bypassing minting cap",
    findingUrl:
      "https://gist.github.com/nican0r/38a7135b02ec608a290bfea1d263ef6f",
    reconLogs:
      "https://github.com/Recon-Fuzz/audits?tab=readme-ov-file#invariant-testing",
    description:
      "Invariant testing helped identify a rounding issue allowing a minting cap to be bypassed",
  },
  {
    project: "TapiocaDAO",
    severity: "Medium",
    findingName: "Incorrect decoding in decodeLockTwpTapDstMsg",
    findingUrl:
      "https://github.com/code-423n4/2024-02-tapioca-findings/issues/69",
    reconLogs:
      "https://getrecon.xyz/shares/eb700590-2f8c-4039-9288-eaff5e0090fe",
    description: "Showing how Invariant tests could have caught this",
  },
];

export default function Trophies() {
  return (
    <Caroussel data={TrophyList}>
      {TrophyList.map((trophy, index) => (
        <Trophy
          key={index}
          project={trophy.project}
          severity={trophy.severity}
          findingName={trophy.findingName}
          findingUrl={trophy.findingUrl}
          reconLogs={trophy.reconLogs}
          description={trophy.description}
        />
      ))}
    </Caroussel>
  );
}
