import React from "react";
import Image from "next/image";
import { Body3, Body4, H3 } from "@/app/components/app-typography";

interface TestimonialProps {
  customer: string;
  feedback: string;
  title: string;
  name: string;
  image: string;
}

export default function Testimonial({
  customer,
  feedback,
  title,
  name,
  image,
}: TestimonialProps) {
  return (
    <div className="relative pt-10">
      <div className="absolute left-1/2 -translate-x-1/2 -translate-y-1/2">
        <Image src={image} alt="company logo" height={72} width={72} />
      </div>
      <div className="mr-7 flex h-full w-[300px] flex-col items-center justify-between overflow-visible rounded-lg border border-accent-alt-primary bg-back-accent-quaternary p-4 text-center md:w-[370px] lg:w-[370px]">
        <div className="mt-10 flex flex-col">
          <Body4 color="primary" className="items-start">
            {feedback}
          </Body4>
        </div>

        <div className="mt-8">
          <H3 color="primary">{customer}</H3>
          <Body3 color="secondary" className="font-thin">
            {name} - {title}
          </Body3>
        </div>
      </div>
    </div>
  );
}
