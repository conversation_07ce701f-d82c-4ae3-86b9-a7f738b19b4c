import Caroussel from "../../../components/Caroussel/Caroussel";

const AuditsList = [
  {
    project: "Liquity",
    githubURL: "https://github.com/Recon-Fuzz/audits/blob/main/bold-report.md",
    privateReport: false,
  },
  {
    project: "Beraborrow",
    githubURL:
      "https://1570492309-files.gitbook.io/~/files/v0/b/gitbook-x-prod.appspot.com/o/spaces%2FffzDCMBDa391vIMqruBP%2Fuploads%2FDLz8dzCO2O7SWGHBDJkj%2FRecon%20Beraborrow.pdf?alt=media&token=6b328d0d-d65f-4f27-913b-d4a867889af7",
    privateReport: false,
  },
  {
    project: "All reports",
    githubURL: "https://github.com/Recon-Fuzz/audits",
    privateReport: false,
  },
  {
    project: "Quill Finance",
    githubURL:
      "https://github.com/Recon-Fuzz/audits/blob/main/Quill_Finance_Report.md",
    privateReport: false,
  },
  {
    project: "Balancer DAO",
    githubURL:
      "https://github.com/Recon-Fuzz/audits/blob/main/Balancer_Report.md",
    privateReport: false,
  },
  {
    project: "Kleidi",
    githubURL:
      "https://github.com/Recon-Fuzz/audits/blob/main/Kleidi_Report.md",
    privateReport: false,
  },
  {
    project: "Apollon",
    githubURL:
      "https://github.com/Recon-Fuzz/audits/blob/main/Apollon_Report.md",
    privateReport: false,
  },
  {
    project: "Credit Coop",
    githubURL: "https://github.com/Recon-Fuzz/audits/blob/main/README.md",
    privateReport: true,
  },
];

import { FaGithub } from "react-icons/fa";
interface TrophyProps {
  project: string;
  githubURL: string;
  privateReport: boolean;
}

function Trophy({ project, githubURL, privateReport }: TrophyProps) {
  return (
    <div className="mr-7 min-h-[272px] w-[300px] flex-none snap-center rounded-lg bg-[#6750A41F] from-purple-800 to-gray-900 p-4 text-white shadow-lg md:w-[370px] lg:w-[370px]">
      <div className="flex h-full flex-col justify-between">
        <div className="grid grid-cols-[3fr_1fr] items-start gap-4">
          <div>
            <p className="text-left text-2xl font-bold">{project}</p>
          </div>
          <div className="flex items-start justify-center">
            <FaGithub className="text-[34px] text-[#D8C7FF]" />
          </div>
        </div>

        <div className=" mt-4 flex flex-col justify-center ">
          <p className="text-xl">Audit</p>
          {privateReport === true ? (
            <p className="text-xl">Private Report</p>
          ) : (
            <a
              href={githubURL}
              target="_blank"
              rel="noreferrer"
              className="block text-lg font-semibold text-[#D0BCFF] underline"
            >
              Report link &gt;
            </a>
          )}
        </div>
      </div>
    </div>
  );
}

export default function Audits() {
  return (
    <Caroussel data={AuditsList}>
      {AuditsList.map((audit, index) => (
        <Trophy
          key={index}
          project={audit.project}
          githubURL={audit.githubURL}
          privateReport={audit.privateReport}
        />
      ))}
    </Caroussel>
  );
}
