const BenefitsArray = [
  {
    title: "THE MISSING PIECE",
    description:
      "Invariant Testing is often the missing piece to reduce the number of bugs protocols go to audit with",
    upcoming: false,
    experimental: false,
  },
  {
    title: "NEVER STUCK IN QUEUE",
    description:
      "Recon Pro allows an unlimited number of parallel runs, we cap the total hours of usage, not the number of concurrent runs",
    upcoming: false,
    experimental: false,
  },
  {
    title: "ONE INTERFACE, ALL OF THE TOOLS",
    description:
      "Echidna, Medusa, Foundry, Halmos and Kontrol, the Recon panel abstracts away the complexity of handling infra, shareable runs, reusable corpus, rpc forks",
    upcoming: false,
    experimental: false,
  },
  {
    title: "DYNAMIC REPLACEMENT",
    description: "Replace values in your tester before running your suites",
    upcoming: false,
    experimental: true,
  },
  {
    title: "LIVE MONITORING",
    description:
      "Convert your Invariant Testing Properties into tests that are checked against each block",
    upcoming: false,
    experimental: true,
  },
  {
    title: "GOVERNANCE FUZZING",
    description:
      "Trigger an invariant testing suite against all of your onChain Proposals.",
    upcoming: false,
    experimental: true,
  },
];

interface BenefitProps {
  title: string;
  description: string;
  upcoming?: boolean;
  experimental?: boolean;
}
function Benefit({ title, description, upcoming, experimental }: BenefitProps) {
  return (
    <div className="relative mx-auto flex h-auto w-full max-w-[448px] flex-col items-center justify-start overflow-hidden rounded-lg bg-[rgba(103,80,164,0.12)] p-5 pt-10 text-center">
      {upcoming && (
        <span className="absolute right-0 top-0 flex h-[36px] w-[176px] items-center justify-center rounded-bl-lg rounded-tr-lg bg-gradient-to-r from-[#EA5A4E] to-[#ED93D3] text-sm font-semibold text-white shadow-md">
          Upcoming Feature
        </span>
      )}
      <div className="flex size-full flex-col justify-between">
        <div className="flex w-full flex-col">
          <h2 className="w-full overflow-hidden break-words text-[24px] font-bold capitalize leading-none text-white md:text-[32px] lg:text-[52px]">
            {title}
          </h2>
        </div>
        <p className="mt-4 break-words text-[14px] text-white lg:text-[18px]">
          {description}
        </p>
        {experimental && (
          <p className="text-center text-[10px] font-light uppercase tracking-wider text-white lg:text-[14px]">
            Experimental
          </p>
        )}
      </div>
    </div>
  );
}

export default function Benefits() {
  return (
    <div className="flex items-center justify-center px-4 py-16">
      <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
        {BenefitsArray.map((benefit, index) => {
          return (
            <Benefit
              key={index}
              title={benefit.title}
              description={benefit.description}
              upcoming={benefit.upcoming}
              experimental={benefit.experimental}
            />
          );
        })}
      </div>
    </div>
  );
}
