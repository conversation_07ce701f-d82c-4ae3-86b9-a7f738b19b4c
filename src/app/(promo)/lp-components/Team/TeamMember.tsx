import { Body2, Title2Strong } from "@/app/components/app-typography";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import { FaGithub } from "react-icons/fa";
import { FaXTwitter } from "react-icons/fa6";

interface TeamMemberProps {
  title: string;
  name: string;
  twitter: string;
  github: string;
  image: string;
  description: string;
}

// Constants for team member configuration
const TEAM_MEMBER_CONFIG = {
  IMAGE_HEIGHT: 160,
  IMAGE_WIDTH: 300,
  ICON_SIZE: 30,
  SOCIAL_ICON_SIZE: 50,
} as const;

// Reusable components
interface SocialLinkProps {
  href: string;
  icon: React.ReactNode;
  label: string;
  className?: string;
}

function SocialLink({ href, icon, label, className }: SocialLinkProps) {
  return (
    <Link
      href={href}
      className={className}
      aria-label={label}
      target="_blank"
      rel="noopener noreferrer"
    >
      <div className="flex size-[50px] items-center justify-center rounded-full bg-accent-primary hover:bg-accent-secondary transition-colors duration-200">
        {icon}
      </div>
    </Link>
  );
}

interface TeamMemberImageProps {
  src: string;
  alt: string;
}

function TeamMemberImage({ src, alt }: TeamMemberImageProps) {
  return (
    <Image
      src={src}
      alt={alt}
      height={TEAM_MEMBER_CONFIG.IMAGE_HEIGHT}
      width={TEAM_MEMBER_CONFIG.IMAGE_WIDTH}
      className="w-full object-cover"
    />
  );
}

interface TeamMemberContentProps {
  name: string;
  description: string;
  twitter?: string;
  github?: string;
}

function TeamMemberContent({
  name,
  description,
  twitter,
  github,
}: TeamMemberContentProps) {
  return (
    <div className="flex grow flex-col justify-center p-4">
      <div className="grow space-y-2">
        <Title2Strong color="primary" className=" uppercase">
          {name}
        </Title2Strong>
        <Body2 color="primary">{description}</Body2>
      </div>

      <div className="mt-4 flex space-x-4">
        {twitter && (
          <SocialLink
            href={twitter}
            icon={<FaXTwitter size={TEAM_MEMBER_CONFIG.ICON_SIZE} />}
            label={`${name} on Twitter`}
          />
        )}
        {github && (
          <SocialLink
            href={github}
            icon={<FaGithub size={TEAM_MEMBER_CONFIG.ICON_SIZE} />}
            label={`${name} on GitHub`}
          />
        )}
      </div>
    </div>
  );
}

export default function TeamMember({
  image,
  name,
  twitter,
  github,
  description,
}: Omit<TeamMemberProps, "title">) {
  return (
    <div className="mr-7 flex max-w-[280px] flex-col rounded-lg border border-accent-primary bg-back-accent-quaternary shadow-lg md:w-[370px] lg:w-[370px]">
      <TeamMemberImage src={image} alt={name} />
      <TeamMemberContent
        name={name}
        description={description}
        twitter={twitter}
        github={github}
      />
    </div>
  );
}
