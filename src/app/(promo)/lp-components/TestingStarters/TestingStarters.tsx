import React from "react";
import Caroussel from "../../../components/Caroussel/Caroussel";
import Starter from "./Starter";

const projectStaters = [
  {
    image: "/recon-logo.svg",
    name: "Create Chimera App",
    description: "The easiest way to scaffold invariant tests",
    github: "https://github.com/Recon-Fuzz/create-chimera-app",
  },
  {
    image: "/recon-logo.svg",
    name: "Eigenlayer Fuzzing",
    description:
      "A plug and play suite to test meaningful Eigenlayer States, a must for every Eigenlayer Integrator",
    github: "https://github.com/Recon-Fuzz/eigenlayer-fuzzing",
  },
  {
    image: "/recon-logo.svg",
    name: "Renzo Fuzzing",
    description:
      "A complete invariant suite for the Renzo Protocol, able to replicate multiple exploits found in subsequent audits",
    github: "https://github.com/Recon-Fuzz/renzo-fuzzing",
  },
  {
    image: "/recon-logo.svg",
    name: "Call Test Undo",
    description:
      "A simple contract meant to turn state changing function calls into invariant tests that do not pollute the story",
    github: "https://github.com/Recon-Fuzz/call-test-undo",
  },
  {
    image: "/recon-logo.svg",
    name: "ERC7540 Reusable Properties",
    description:
      "Simple to Reuse, high level properties for ERC7540 Vaults, Built in Collaboration with Centrifuge",
    github: "https://github.com/Recon-Fuzz/erc7540-reusable-properties",
  },
  {
    image: "/recon-logo.svg",
    name: "Chimera",
    description:
      "Open Source Framework to unify Invariant, Fuzz and Formal Verification into a single, write once run everywhere API",
    github: "https://github.com/Recon-Fuzz/chimera",
  },
];

export default function TestingStarters() {
  return (
    <Caroussel data={projectStaters}>
      {projectStaters.map((project, index) => {
        return (
          <Starter
            key={index}
            image={project.image}
            name={project.name}
            description={project.description}
            github={project.github}
          />
        );
      })}
    </Caroussel>
  );
}
