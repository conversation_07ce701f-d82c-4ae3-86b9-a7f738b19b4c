"use client";

import type { ReactNode } from "react";
import { useEffect } from "react";
import { THEME_OPTIONS } from "../services/ThemeProvider";
import { usePersistedTheme } from "../services/useThemePersistence";

interface PromoLayoutProps {
  children: ReactNode;
}

export function PromoLayout({ children }: PromoLayoutProps) {
  const { setTheme, mounted } = usePersistedTheme();

  // Force dark theme for all promo pages
  useEffect(() => {
    if (mounted) {
      setTheme(THEME_OPTIONS.dark);
    }
  }, [mounted, setTheme]);

  return <div className="promo-layout">{children}</div>;
}
