"use client";

import { Body1, Body2, H1, H2 } from "../../components/app-typography";
import { LPGradientBackground } from "../../components/gradient-wrapper";
import PromoNavbar from "../lp-components/PromoNavbar";

export default function FuzzFest() {
  return (
    <div>
      <PromoNavbar />
      <div className="main-container w-full overflow-x-hidden">
        <LPGradientBackground />
        <main className="relative z-10">
          <section className="mx-auto mb-12 flex w-[87.5%] flex-col items-center justify-center pt-20 lg:w-4/5 lg:pt-44">
            <H1 className="mb-8 text-center text-accent-primary">FuzzFest</H1>
            <H2 className="mb-8 text-center text-fore-neutral-primary">
              Over 250 Registered!
            </H2>

            <div className="mb-8 w-full max-w-4xl">
              <div className="relative aspect-video w-full overflow-hidden rounded-lg bg-back-neutral-tertiary">
                <iframe
                  className="absolute inset-0 h-full w-full"
                  src="https://www.youtube.com/embed/Cqmu-mhSLt8?si=dT1TFNZzPZhEtjaN"
                  title="Recon FuzzFest"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                  allowFullScreen
                />
              </div>
            </div>

            <div className="mb-8 text-center space-y-4">
              <Body1 className="text-fore-neutral-primary">
                {`A 3.45 hours event with the "crème de la crème" of the fuzzing scene`}
              </Body1>
              <Body1 className="text-accent-primary font-semibold">
                December 16th, 2024
              </Body1>
              <Body1 className="text-fore-neutral-secondary">
                Current Schedule Outline:
              </Body1>
            </div>
            <div className="w-full max-w-3xl">
              <div className="space-y-4 rounded-lg bg-back-neutral-tertiary p-8 border border-stroke-neutral-decorative">
                <div className="flex flex-col space-y-3">
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 rounded-md bg-back-neutral-secondary border border-stroke-neutral-decorative">
                    <Body2 className="text-accent-primary font-semibold">
                      11:45 AM UTC
                    </Body2>
                    <Body2 className="text-fore-neutral-primary">Intro</Body2>
                  </div>

                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 rounded-md bg-back-neutral-secondary border border-stroke-neutral-decorative">
                    <Body2 className="text-accent-primary font-semibold">
                      12:00 PM UTC
                    </Body2>
                    <Body2 className="text-fore-neutral-primary">
                      Dacian: Epic Fuzz Testing Workshop
                    </Body2>
                  </div>

                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 rounded-md bg-back-neutral-secondary border border-stroke-neutral-decorative">
                    <Body2 className="text-accent-primary font-semibold">
                      1:00 PM UTC
                    </Body2>
                    <Body2 className="text-fore-neutral-primary">
                      Josselin: Contributing to Medusa
                    </Body2>
                  </div>

                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 rounded-md bg-back-neutral-secondary border border-stroke-neutral-decorative">
                    <Body2 className="text-accent-primary font-semibold">
                      1:30 PM UTC
                    </Body2>
                    <Body2 className="text-fore-neutral-primary">
                      Alex: A glimpse into the future of Invariant Testing
                    </Body2>
                  </div>

                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 rounded-md bg-back-neutral-secondary border border-stroke-neutral-decorative">
                    <Body2 className="text-accent-primary font-semibold">
                      2:00 PM UTC
                    </Body2>
                    <Body2 className="text-fore-neutral-primary">
                      Benjamin: Review of the UniV4 Invariant Testing Suite
                    </Body2>
                  </div>

                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 rounded-md bg-back-neutral-secondary border border-stroke-neutral-decorative">
                    <Body2 className="text-accent-primary font-semibold">
                      2:45 PM UTC
                    </Body2>
                    <Body2 className="text-fore-neutral-primary">Wrap-up</Body2>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </main>
      </div>
    </div>
  );
}
