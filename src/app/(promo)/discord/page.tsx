"use client";

import Link from "next/link";
import { AppButton } from "../../components/app-button";
import { LPGradientBackground } from "../../components/gradient-wrapper";
import { useGetDiscord } from "../../services/discord.hook.ts";
import PromoNavbar from "../lp-components/PromoNavbar";

export default function Discord() {
  const { data: discordUrl } = useGetDiscord();

  return (
    <div>
      <PromoNavbar />
      <div className="main-container w-full overflow-x-hidden">
        <LPGradientBackground />
        <main className="relative z-10">
          <section className="mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center pt-[80px] lg:w-4/5 lg:pt-[180px]">
            <h1 className="main-title-custom mb-5 text-center text-[56px] font-bold leading-[48px] tracking-normal lg:text-[120px] lg:leading-[100px]">
              Recon Discord
            </h1>
            <h2 className="main-title-custom mb-5 text-center text-[32px] font-bold leading-[48px] tracking-normal">
              400 Register and Growing!
            </h2>
            {discordUrl ? (
              <Link href={discordUrl}>
                <AppButton>Join the discord</AppButton>
              </Link>
            ) : (
              <h2>No Discord link</h2>
            )}
          </section>
        </main>
      </div>
    </div>
  );
}
