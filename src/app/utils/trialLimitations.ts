import type { Job } from "@/app/services/jobs.hooks";

export interface TrialLimitationCheck {
  canRun: boolean;
  message: string | null;
}

export function checkTrialLimitations(
  billingStatus: string,
  allJobs: Job[]
): TrialLimitationCheck {
  if (billingStatus !== "TRIAL") {
    return { canRun: true, message: null };
  }

  const runningJobs = allJobs.filter(
    (job) =>
      job.status === "RUNNING" ||
      job.status === "STARTED" ||
      job.status === "QUEUED"
  );

  if (runningJobs.length > 0) {
    const runningJobsList = runningJobs
      .map((job) => `${job.label}-${job.id}`)
      .join(", ");

    return {
      canRun: false,
      message: `You can only run one job at a time, currently running: ${runningJobsList}`,
    };
  }

  return { canRun: true, message: null };
}

export function showTrialLimitationAlert(message: string): void {
  alert(message);
}
