"use client";

import axios from "axios";
import { useCallback } from "react";

import { ENV_TYPE } from "@/app/app.constants";
import type { GitHubLinkFormValues } from "@/app/components/create-job-form/types";
import { checkFields } from "@/utils/fieldChecker";
import {
  checkTrialLimitations,
  showTrialLimitationAlert,
} from "@/app/utils/trialLimitations";
import type { Job } from "@/app/services/jobs.hooks";

interface UseJobSubmissionProps {
  organization: { billingStatus: string };
  allJobs: Job[];
  setJobId: (id: number | null) => void;
  refetchJobs: () => void;
}

export function useJobSubmission({
  organization,
  allJobs,
  setJobId,
  refetchJobs,
}: UseJobSubmissionProps) {
  const startEchidnaAbiJob = useCallback(
    async (values: GitHubLinkFormValues) => {
      const trialCheck = checkTrialLimitations(
        organization.billingStatus,
        allJobs
      );
      if (!trialCheck.canRun && trialCheck.message) {
        showTrialLimitationAlert(trialCheck.message);
        return;
      }

      const {
        pathToTester,
        echidnaConfig,
        contract,
        corpusDir,
        forkBlock,
        forkMode,
        forkReplacement,
        ref,
        repoName,
        rpcUrl,
        testLimit,
        testMode,
        preprocess,
        directory,
        orgName,
        targetCorpus,
        label,
        prepareContracts,
      } = values;

      const fuzzerArgs = {
        pathToTester,
        config: echidnaConfig,
        contract,
        corpusDir,
        forkBlock,
        forkMode,
        forkReplacement,
        rpcUrl,
        testLimit,
        testMode,
        preprocess,
        targetCorpus,
        label,
        prepareContracts,
        govFuzz: true,
      };

      const areBasicFieldsOK = checkFields(
        orgName,
        repoName,
        ref,
        prepareContracts
      );
      if (!areBasicFieldsOK) return;

      try {
        const foundData = await axios({
          method: "POST",
          url: `/api/jobs/echidna`,
          data: {
            directory,
            orgName,
            repoName,
            ref,
            fuzzerArgs,
            preprocess,
          },
        });

        setJobId(foundData?.data?.data?.id as number);
        refetchJobs();
      } catch (e: any) {
        console.log("e", e);
        alert(`Something went wrong: ${e.response.data.message}`);
      }
    },
    [organization?.billingStatus, allJobs, setJobId, refetchJobs]
  );

  const startFoundryJob = useCallback(
    async (values: GitHubLinkFormValues) => {
      const trialCheck = checkTrialLimitations(
        organization.billingStatus,
        allJobs
      );
      if (!trialCheck.canRun && trialCheck.message) {
        showTrialLimitationAlert(trialCheck.message);
        return;
      }

      const {
        contract,
        forkBlock,
        forkMode,
        ref,
        repoName,
        preprocess,
        directory,
        orgName,
        rpcUrl,
        runs,
        seed,
        verbosity,
        testCommand,
        testTarget,
        prepareContracts,
      } = values;

      const fuzzerArgs = {
        contract,
        forkBlock,
        forkMode,
        preprocess,
        rpcUrl,
        runs,
        seed,
        verbosity,
        testCommand,
        testTarget,
        prepareContracts,
        govFuzz: true,
      };

      const areBasicFieldsOK = checkFields(
        orgName,
        repoName,
        ref,
        prepareContracts
      );
      if (!areBasicFieldsOK) return;

      try {
        const foundData = await axios({
          method: "POST",
          url: `/api/jobs/foundry`,
          data: {
            directory,
            orgName,
            repoName,
            ref,
            fuzzerArgs,
            preprocess,
          },
        });

        setJobId(foundData?.data?.data?.id as number);
        refetchJobs();
      } catch (e: any) {
        console.log("e", e);
        alert(`Something went wrong: ${e.response.data.message}`);
      }
    },
    [organization?.billingStatus, allJobs, setJobId, refetchJobs]
  );

  const startHalmosJob = useCallback(
    async (values: GitHubLinkFormValues) => {
      const trialCheck = checkTrialLimitations(
        organization.billingStatus,
        allJobs
      );
      if (!trialCheck.canRun && trialCheck.message) {
        showTrialLimitationAlert(trialCheck.message);
        return;
      }

      const {
        contract,
        ref,
        repoName,
        preprocess,
        directory,
        orgName,
        halmosPrefix,
        halmosArray,
        halmosLoops,
        verbosity,
        prepareContracts,
      } = values;

      const fuzzerArgs = {
        contract,
        preprocess,
        halmosArray,
        halmosLoops,
        halmosPrefix,
        verbosity,
        prepareContracts,
      };

      try {
        const foundData = await axios({
          method: "POST",
          url: `/api/jobs/halmos`,
          data: {
            directory,
            orgName,
            repoName,
            ref,
            fuzzerArgs,
            preprocess,
          },
        });

        setJobId(foundData?.data?.data?.id as number);
        refetchJobs();
      } catch (e: any) {
        console.log("e", e);
        alert("Something went wrong");
      }
    },
    [organization?.billingStatus, allJobs, setJobId, refetchJobs]
  );

  const startKontrolJob = useCallback(
    async (values: GitHubLinkFormValues) => {
      const trialCheck = checkTrialLimitations(
        organization.billingStatus,
        allJobs
      );
      if (!trialCheck.canRun && trialCheck.message) {
        showTrialLimitationAlert(trialCheck.message);
        return;
      }

      const {
        contract,
        ref,
        repoName,
        preprocess,
        directory,
        orgName,
        kontrolTest,
        prepareContracts,
      } = values;

      const fuzzerArgs = {
        contract,
        preprocess,
        kontrolTest,
        prepareContracts,
      };

      try {
        const foundData = await axios({
          method: "POST",
          url: `/api/jobs/kontrol`,
          data: {
            directory,
            orgName,
            repoName,
            ref,
            fuzzerArgs,
            preprocess,
          },
        });

        setJobId(foundData?.data?.data?.id as number);
        refetchJobs();
      } catch (e: any) {
        console.log("e", e);
        alert("Something went wrong");
      }
    },
    [organization?.billingStatus, allJobs, setJobId, refetchJobs]
  );

  const startMedusaAbiJob = useCallback(
    async (values: GitHubLinkFormValues) => {
      const trialCheck = checkTrialLimitations(
        organization.billingStatus,
        allJobs
      );
      if (!trialCheck.canRun && trialCheck.message) {
        showTrialLimitationAlert(trialCheck.message);
        return;
      }

      const {
        orgName,
        repoName,
        ref,
        directory,
        medusaConfig,
        timeout,
        preprocess,
        targetCorpus,
        label,
        prepareContracts,
      } = values;

      const fuzzerArgs = {
        timeout,
        config: medusaConfig,
        targetCorpus,
        prepareContracts,
        govFuzz: true,
      };

      const areBasicFieldsOK = checkFields(
        orgName,
        repoName,
        ref,
        prepareContracts
      );
      if (!areBasicFieldsOK) return;

      console.log("prepareContracts", prepareContracts);
      try {
        const foundData = await axios({
          method: "POST",
          url: `/api/jobs/medusa`,
          data: {
            fuzzerArgs,
            preprocess,
            orgName,
            repoName,
            ref,
            directory,
            label,
          },
        });

        refetchJobs();
      } catch (e: any) {
        alert(`Something went wrong: ${e.response.data.message}`);
      }
    },
    [organization?.billingStatus, allJobs, refetchJobs]
  );

  const getSubmissionHandler = useCallback(
    (env: ENV_TYPE) => {
      switch (env) {
        case ENV_TYPE.MEDUSA:
          return startMedusaAbiJob;
        case ENV_TYPE.ECHIDNA:
          return startEchidnaAbiJob;
        case ENV_TYPE.FOUNDRY:
          return startFoundryJob;
        case ENV_TYPE.HALMOS:
          return startHalmosJob;
        case ENV_TYPE.KONTROL:
          return startKontrolJob;
        default:
          return startMedusaAbiJob;
      }
    },
    [
      startMedusaAbiJob,
      startEchidnaAbiJob,
      startFoundryJob,
      startHalmosJob,
      startKontrolJob,
    ]
  );

  return {
    startEchidnaAbiJob,
    startFoundryJob,
    startHalmosJob,
    startKontrolJob,
    startMedusaAbiJob,
    getSubmissionHandler,
  };
}
