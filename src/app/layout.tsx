import "./globals.css";

import type { Metada<PERSON> } from "next";

import { AppProviders } from "./app-providers";
import AuthProvider from "./context/AuthProvider";
import QueryProvider from "./services/QueryProvider";
import type { ReactNode } from "react";
import { blenderPro } from "./app.constants";

export const metadata: Metadata = {
  title: "Recon",
  description: "Recon helps you build and run invariant tests",
};

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <AuthProvider>
        <QueryProvider>
          <body className={blenderPro.className}>
            <AppProviders>{children}</AppProviders>
          </body>
        </QueryProvider>
      </AuthProvider>
    </html>
  );
}
