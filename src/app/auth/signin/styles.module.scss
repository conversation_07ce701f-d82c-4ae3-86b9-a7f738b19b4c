.gradientButton {
  border: none;
  padding: 10px;
  border-radius: 5px;
  cursor: pointer;
  position: relative;
  z-index: 1;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.3s ease;
  display: flex;
  width: 100%;
  justify-content: center;
  align-items: center;
  margin: 10px;
  color: var(--fore-neutral-primary);
  background: linear-gradient(
    277.21deg,
    var(--gradient-primary-start) -13.33%,
    var(--gradient-primary-end) 51.57%
  );
}

.gradientButton::before {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    277.21deg,
    var(--gradient-primary-end) -13.33%,
    var(--gradient-primary-start) 51.57%,
    var(--gradient-primary-end) 116.47%
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  z-index: -1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.gradientButton:hover {
  transform: scale(1.05);
  background: linear-gradient(
    277.21deg,
    var(--gradient-primary-start) -13.33%,
    var(--gradient-primary-end) 51.57%
  );
}
