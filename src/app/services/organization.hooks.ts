import { useQuery } from "@tanstack/react-query";
import axios from "axios";
import { signOut } from "next-auth/react";

// import { getOrgMock } from "../mocks/getOrg";

export interface Organization {
  id: string;
  billingStatus: "UNPAID" | "PAID" | "REVOKED" | "TRIAL";

  // ONLY IF PRO
  billingUpdateAt: string; // Date Time
  totalMinutesLeft: number;
  name: string;
  users?: {
    id: string;
    organizationId: string;
  }[];
}

export function useGetMyOrgInternal({
  redirectOnFail = true,
}: {
  redirectOnFail?: boolean;
}): {
  data: Organization;
  isLoading: boolean;
  refetch: any;
  isRefetching: boolean;
} {
  return useQuery<Organization, string>({
    queryKey: ["getOrg"],
    queryFn: async () => {
      try {
        const res = await axios.get("/api/organizations/my");
        return res.data.data;
      } catch (e) {
        // return getOrgMock;
        if (e.response.status === 404) {
          // We know it means there's no org, that's ok
          return {};
        }
        // Session is invalid, log out
        if (redirectOnFail) {
          signOut({ callbackUrl: "/" });
        }
        throw "Unable to get organization";
      }
    },
  });
}

export enum OrgStatus {
  NOORG,
  FREE,
  PAID,
  TRIAL,
}

const getOrgStatus = (org: Organization) => {
  if (org?.billingStatus) {
    if (org.billingStatus === "PAID") {
      return OrgStatus.PAID;
    } else if (org.billingStatus === "TRIAL") {
      return OrgStatus.TRIAL;
    } else {
      return OrgStatus.FREE;
    }
  }

  return OrgStatus.NOORG;
};

export function useGetMyOrg(
  { redirectOnFail }: { redirectOnFail?: boolean } = {
    redirectOnFail: true,
  }
): {
  data: Organization;
  isLoading: boolean;
  refetch: any;
  isRefetching: boolean;
  orgStatus: OrgStatus;
} {
  const { data, isLoading, refetch, isRefetching } = useGetMyOrgInternal({
    redirectOnFail,
  });

  return {
    data: data,
    isLoading,
    refetch,
    isRefetching,
    orgStatus: getOrgStatus(data),
    // orgStatus: OrgStatus.PAID,
  };
}
