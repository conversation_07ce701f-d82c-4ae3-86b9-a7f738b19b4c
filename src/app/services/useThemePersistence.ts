"use client";

import { useTheme } from "next-themes";
import { useEffect, useState } from "react";
import { THEME_OPTIONS } from "./ThemeProvider";

/**
 * Simplified theme hook that works with next-themes
 * Based on the official next-themes documentation pattern
 */
export function usePersistedTheme() {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // useEffect only runs on the client, so now we can safely show the UI
  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render theme-dependent UI until mounted to avoid hydration mismatch
  if (!mounted) {
    return {
      theme: THEME_OPTIONS.dark, // Default fallback
      setTheme,
      mounted: false,
      isDark: true, // Default fallback
    };
  }

  // After mounting, use the actual theme from next-themes
  const currentTheme = (theme as THEME_OPTIONS) || THEME_OPTIONS.dark;
  const isDark = currentTheme === THEME_OPTIONS.dark;

  return {
    theme: currentTheme,
    setTheme,
    mounted: true,
    isDark,
  };
}
