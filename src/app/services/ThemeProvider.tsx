"use client";

import { ThemeProvider as NextThemesProvider } from "next-themes";
import { type ThemeProviderProps } from "next-themes/dist/types";

export const LOCAL_STORAGE_KEYS = {
  theme: "recon/theme/v1",
};

export const COMING_SOON_LABEL = "Coming Soon!";

export enum THEME_OPTIONS {
  light = "light",
  dark = "dark",
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  return (
    <NextThemesProvider
      attribute="class"
      defaultTheme={THEME_OPTIONS.dark}
      storageKey={LOCAL_STORAGE_KEYS.theme}
      themes={[THEME_OPTIONS.light, THEME_OPTIONS.dark]}
      enableColorScheme={false}
      disableTransitionOnChange
      enableSystem={false}
    >
      {children}
    </NextThemesProvider>
  );
}
